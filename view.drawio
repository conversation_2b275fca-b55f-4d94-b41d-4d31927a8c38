<mxfile host="65bd71144e">
    <diagram id="-X1Vu6vy5pyaSu6XPrnt" name="Page-1">
        <mxGraphModel dx="508" dy="446" grid="1" gridSize="10" guides="1" tooltips="1" connect="1" arrows="1" fold="1" page="1" pageScale="1" pageWidth="827" pageHeight="1169" math="0" shadow="0">
            <root>
                <mxCell id="0"/>
                <mxCell id="1" parent="0"/>
                <mxCell id="5" value="" style="edgeStyle=none;html=1;fontSize=7;" parent="1" source="2" target="4" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="2" value="&lt;font style=&quot;font-size: 7px;&quot;&gt;MenuMaster&amp;nbsp; (&amp;nbsp;&lt;span style=&quot;font-size: 7px;&quot;&gt;Live Casino,....)&lt;/span&gt;&lt;/font&gt;" style="rounded=0;whiteSpace=wrap;html=1;align=center;fontSize=7;labelBackgroundColor=default;fillColor=#cdeb8b;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="150" y="60" width="130" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="3" value="Super Admin" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;fontColor=#ffffff;strokeColor=#005700;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="150" y="20" width="130" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="9" value="" style="edgeStyle=none;html=1;fontSize=7;" parent="1" source="4" target="8" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="4" value="&lt;div style=&quot;color: rgb(188, 190, 196); font-size: 7px;&quot;&gt;&lt;pre style=&quot;font-family: &amp;quot;JetBrains Mono&amp;quot;, monospace; font-size: 7px;&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;menuTenantSetting&lt;/font&gt;&lt;/pre&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=0;fontSize=7;fillColor=#cdeb8b;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="400" y="60" width="115" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="6" value="Admin" style="rounded=0;whiteSpace=wrap;html=1;fillColor=#008a00;fontColor=#ffffff;strokeColor=#005700;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="394" y="21" width="130" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="7" value="id&amp;nbsp; &amp;nbsp;= memu_id" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="295" y="50" width="70" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="35" value="" style="edgeStyle=none;html=1;fontSize=7;" parent="1" source="37" target="34" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="38" value="" style="edgeStyle=none;html=1;fontSize=7;" parent="1" source="8" target="37" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="8" value="&lt;div style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; line-height: 19px; font-size: 7px;&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;pages&lt;/font&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=0;fontSize=7;fillColor=#cdeb8b;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="397.5" y="130" width="120" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="10" value="&lt;font style=&quot;font-size: 7px; color: light-dark(rgb(0, 0, 0), rgb(255, 255, 255));&quot;&gt;id =&amp;nbsp;&lt;span style=&quot;font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; font-size: 7px;&quot;&gt;top_menu_id&lt;/span&gt;&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="450" y="100" width="80" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="12" value="&lt;pre style=&quot;color: rgb(188, 190, 196); text-align: left; font-family: &amp;quot;JetBrains Mono&amp;quot;, monospace; font-size: 7px;&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;casino_providers(&lt;/font&gt;Ezugi)&lt;/pre&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="150" y="220" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="13" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 800; line-height: 1.2; color: rgb(33, 37, 41); font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 7px;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;&amp;nbsp; All Casino Providers&amp;nbsp; &amp;nbsp;&lt;/font&gt;&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="160" y="190" width="120" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="15" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 800; line-height: 1.2; color: rgb(33, 37, 41); font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 7px;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;All Casino Games&lt;/font&gt;&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="170" y="280" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="16" value="&lt;pre style=&quot;color: rgb(188, 190, 196); text-align: left; font-family: &amp;quot;JetBrains Mono&amp;quot;, monospace; font-size: 7px;&quot;&gt;&lt;div style=&quot;font-size: 7px;&quot;&gt;&lt;pre style=&quot;font-family: &amp;quot;JetBrains Mono&amp;quot;, monospace; font-size: 7px;&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;casino_games  &lt;/font&gt;&lt;/pre&gt;&lt;/div&gt;&lt;/pre&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="149" y="307" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="17" value="id = casino_provider_id" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="110" y="255" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="18" value="" style="endArrow=classic;html=1;fontSize=7;entryX=0.5;entryY=0;entryDx=0;entryDy=0;fillColor=#e3c800;strokeColor=#B09500;" parent="1" source="12" target="16" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="340" y="310" as="sourcePoint"/>
                        <mxPoint x="390" y="260" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="19" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 800; line-height: 1.2; color: rgb(33, 37, 41); font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial; font-size: 7px;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;All Casino Tables&lt;/font&gt;&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="172" y="360" width="88" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="20" value="&lt;pre style=&quot;text-align: left; font-size: 7px;&quot;&gt;&lt;div style=&quot;font-size: 7px;&quot;&gt;&lt;pre style=&quot;color: rgb(188, 190, 196); font-family: &amp;quot;JetBrains Mono&amp;quot;, monospace; font-size: 7px;&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;casino_tables&lt;/font&gt;&lt;/pre&gt;&lt;/div&gt;&lt;/pre&gt;" style="rounded=1;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="150" y="394" width="140" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="21" value="" style="endArrow=classic;html=1;fontSize=7;exitX=0.484;exitY=1.033;exitDx=0;exitDy=0;exitPerimeter=0;entryX=0.473;entryY=0;entryDx=0;entryDy=0;entryPerimeter=0;fillColor=#e3c800;strokeColor=#B09500;" parent="1" source="16" target="20" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="340" y="430" as="sourcePoint"/>
                        <mxPoint x="390" y="380" as="targetPoint"/>
                    </mxGeometry>
                </mxCell>
                <mxCell id="24" value="&lt;font style=&quot;font-size: 7px;&quot;&gt;table_id(UUID)&lt;/font&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="167" y="420" width="70" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="26" value="" style="endArrow=classic;html=1;fontSize=7;exitX=0;exitY=0.5;exitDx=0;exitDy=0;entryX=0;entryY=0.5;entryDx=0;entryDy=0;" parent="1" source="12" target="20" edge="1">
                    <mxGeometry width="50" height="50" relative="1" as="geometry">
                        <mxPoint x="210" y="360" as="sourcePoint"/>
                        <mxPoint x="260" y="310" as="targetPoint"/>
                        <Array as="points">
                            <mxPoint x="90" y="235"/>
                            <mxPoint x="90" y="410"/>
                        </Array>
                    </mxGeometry>
                </mxCell>
                <mxCell id="27" value="&lt;font style=&quot;font-size: 7px;&quot;&gt;id=&amp;nbsp;provider_id&lt;/font&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="26" y="360" width="70" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="28" value="game_id = game_id" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="205" y="340" width="90" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="29" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 800; line-height: 1.2; color: rgb(33, 37, 41); font-size: 7px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;Casino Pages&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="400" y="41" width="60" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="32" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 800; line-height: 1.2; color: rgb(33, 37, 41); font-size: 7px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;Casino Page Items&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="369" y="105" width="90" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="34" value="&lt;div style=&quot;color: rgb(188, 190, 196); font-size: 7px;&quot;&gt;&lt;pre style=&quot;font-family: &amp;quot;JetBrains Mono&amp;quot;, monospace; font-size: 7px;&quot;&gt;&lt;div style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; line-height: 19px; font-size: 7px;&quot;&gt;&lt;font style=&quot;font-size: 7px;&quot;&gt;casino_menus&lt;/font&gt;&lt;/div&gt;&lt;/pre&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;rounded=0;fontSize=7;fillColor=#cdeb8b;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="591.25" y="205" width="82.5" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="36" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; line-height: 1.2; color: rgb(33, 37, 41); font-size: 7px; background-color: rgb(255, 255, 255);&quot; class=&quot;m-0 custom-page-heading&quot;&gt;&amp;nbsp; Casino Page Menus (Name)&amp;nbsp;&amp;nbsp;&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="575" y="166" width="115" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="45" value="" style="edgeStyle=none;html=1;fontSize=5;" parent="1" source="37" target="44" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="37" value="&lt;span style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; text-align: left;&quot;&gt;page_menus&lt;/span&gt;" style="whiteSpace=wrap;html=1;rounded=0;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="397.5" y="205" width="120" height="35" as="geometry"/>
                </mxCell>
                <mxCell id="39" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 800; line-height: 1.2; color: rgb(33, 37, 41); font-size: 7px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;Casino Page Menus&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="357" y="186" width="100" height="20" as="geometry"/>
                </mxCell>
                <mxCell id="42" value="&lt;div style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; line-height: 19px;&quot;&gt;&lt;font style=&quot;font-size: 8px;&quot;&gt;pages.id = page_menus.page_id&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=7;" parent="1" vertex="1">
                    <mxGeometry x="300" y="165" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="43" value="&lt;div style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; line-height: 19px;&quot;&gt;&lt;font style=&quot;font-size: 5px;&quot;&gt; page_menus.casino_menu_id = casino_menus.id&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=8;" parent="1" vertex="1">
                    <mxGeometry x="490" y="175" width="150" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="49" value="" style="edgeStyle=none;html=1;fontSize=10;" parent="1" source="44" target="48" edge="1">
                    <mxGeometry relative="1" as="geometry"/>
                </mxCell>
                <mxCell id="44" value="&lt;div style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; line-height: 19px; font-size: 9px;&quot;&gt;&lt;font style=&quot;font-size: 9px;&quot;&gt;menu_items&lt;/font&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=9;rounded=0;fillColor=#cdeb8b;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="397.5" y="307" width="122.5" height="22.5" as="geometry"/>
                </mxCell>
                <mxCell id="46" value="&lt;div style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; line-height: 19px;&quot;&gt;&lt;font style=&quot;font-size: 6px;&quot;&gt;page_menus.id = menu_items.page_menu_id&lt;/font&gt;&lt;/div&gt;" style="text;html=1;align=center;verticalAlign=middle;resizable=0;points=[];autosize=1;strokeColor=none;fillColor=none;fontSize=11;" parent="1" vertex="1">
                    <mxGeometry x="310" y="270" width="160" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="47" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 500; line-height: 1.2; color: rgb(33, 37, 41); font-size: 9px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;Casino Page Menu Items&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=9;" parent="1" vertex="1">
                    <mxGeometry x="340" y="288.25" width="120" height="21.75" as="geometry"/>
                </mxCell>
                <mxCell id="48" value="&lt;div style=&quot;color: rgb(204, 204, 204); font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; line-height: 19px; font-size: 9px;&quot;&gt;&lt;font style=&quot;font-size: 9px;&quot;&gt;casino_items&lt;/font&gt;&lt;/div&gt;" style="whiteSpace=wrap;html=1;fontSize=9;rounded=0;fillColor=#cdeb8b;strokeColor=#36393d;" parent="1" vertex="1">
                    <mxGeometry x="601.25" y="303.25" width="118.75" height="30" as="geometry"/>
                </mxCell>
                <mxCell id="50" value="&lt;div style=&quot;font-family: &amp;quot;Droid Sans Mono&amp;quot;, &amp;quot;monospace&amp;quot;, monospace; font-weight: normal; font-size: 9px; line-height: 19px;&quot;&gt;&lt;div style=&quot;font-size: 9px;&quot;&gt;&lt;span style=&quot;font-size: 9px;&quot;&gt; menu_items.casino_item_id = casino_items.id&lt;/span&gt;&lt;/div&gt;&lt;/div&gt;" style="text;whiteSpace=wrap;html=1;fontSize=9;labelBorderColor=none;fontColor=light-dark(#000000,#1A1A1A);" parent="1" vertex="1">
                    <mxGeometry x="575" y="263.25" width="400" height="40" as="geometry"/>
                </mxCell>
                <mxCell id="51" value="&lt;h1 style=&quot;box-sizing: border-box; margin: 0px; font-family: Poppins, sans-serif; font-weight: 500; line-height: 1.2; color: rgb(33, 37, 41); font-size: 9px; font-style: normal; font-variant-ligatures: normal; font-variant-caps: normal; letter-spacing: normal; orphans: 2; text-align: left; text-indent: 0px; text-transform: none; widows: 2; word-spacing: 0px; -webkit-text-stroke-width: 0px; background-color: rgb(255, 255, 255); text-decoration-thickness: initial; text-decoration-style: initial; text-decoration-color: initial;&quot; class=&quot;m-0 custom-page-heading&quot;&gt;Casino Page Menu Items(uuid)&lt;/h1&gt;" style="text;whiteSpace=wrap;html=1;fontSize=9;" parent="1" vertex="1">
                    <mxGeometry x="601.25" y="285.25" width="148.75" height="21.75" as="geometry"/>
                </mxCell>
            </root>
        </mxGraphModel>
    </diagram>
</mxfile>