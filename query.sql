
-- -- -- CASE 1: STATUS API QUEUE -  payin_deposit_update_status(user_id:16754, deposit_request_id:1855, status:opened)

--         -- DELETE FROM public.user_bonus_queue WHERE user_id = 16754;

--         -- INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES ( 16754, 1492, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1665, null);
--         -- INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1490, 0, '2025-01-23 06:59:29', '2025-01-27 15:28:49', null, null, null, 1666, null);
--         -- INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1491, 0, '2025-01-23 06:59:38', '2025-01-27 15:28:49', null, null, null, 1664, null);
--         -- -- 
--         -- DELETE FROM public.user_bonus WHERE user_id = 16754;

--         -- INSERT INTO public.user_bonus ( status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES ('claimed', 300, 0, 16754, 1473, 'deposit', '2025-01-31 18:29:00.000000', '2025-01-16 08:59:46.209000', '2025-01-16 08:33:42.509000', '2025-01-16 08:59:46.238000', null, 600, null);
--         -- INSERT INTO public.user_bonus (status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES ('claimed', 125, 0, 16754, 1221, 'deposit', '2025-01-31 12:59:00.000000', null, '2025-01-24 15:04:14.000000', '2025-01-24 15:04:14.000000', null, 250, null);


--         -- delete from transactions where debit_transaction_id ='7B5C7721-4178-4CFF-A1E4-AF8774C442FC';
--         -- UPDATE public.deposit_requests SET user_id = 16754::bigint, status='opened' WHERE id = 1855::bigint returning id, user_id, order_id;
--         -- INSERT INTO "queue_logs" ( "type", "status", "ids", "created_at", "updated_at", "tenant_id") VALUES
--         -- ('payin_deposit_update_status',	0,	'[1855]',	'2025-01-27 11:25:22',	'2025-01-27 11:25:23',	NULL) returning type, status, ids;
--         -- UPDATE public.deposit_requests SET user_id = 16754::bigint WHERE id = 1855::bigint returning id, user_id, order_id;
--         -- UPDATE public.deposit_requests SET status = 'opened'::varchar(255) WHERE id = 1855::bigint;
-- -- -- ****************************






-- -- CASE 2 : ORDER INSTANT DEPOSIT BONUS
--     --     delete from user_bonus where  bonus_id in (1530, 1529, 1528, 1527);
--     --     delete from  losing_bonus_tiers where losing_bonus_setting_id in (select id
--     --     from losing_bonus_settings where bonus_id in (1530, 1529, 1528, 1527));
--     --     delete from losing_bonus_settings where bonus_id in  (1530, 1529, 1528, 1527);
--     --     delete from deposit_bonus_settings where id in (913, 914, 915, 916);
--     --     delete from deposit_bonus_settings where deposit_bonus_settings.bonus_id in  (1530, 1529, 1528, 1527);
--     --     delete from bonus where id in  (1530, 1529, 1528, 1527);
--     --     delete  from user_bonus_queue where user_id =16754;
--     --     delete from queue_logs where status in ( 0, 1, 2, 3);
--     --     delete from losing_bonus_settings where bonus_id in  (1530, 1529, 1528, 1527);
--     --     update user_bonus set status ='claimed' where user_id = 16754;

--     --    INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1527, 'TESTINSTANT', 50, true, '2025-01-27 16:33:00.000000', '2026-07-23 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TEST_INSTANT"}', 'tenants/bonus/dbd2324c-ec9a-46e4-a1f8-5cdaa52d3b94____2X1.png', '{"EN-US": "<p>TEST_INSTANT</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:32:17.000000', '2025-01-27 16:32:19.000000', 16, null, 0, 1, null, 'instant', null, null, null, null, false, false);
--     --     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1528, 'TESTINSTANTREC', 50, true, '2025-07-27 16:37:00.000000', '2026-09-25 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TESTINSTANTREC"}', 'tenants/bonus/2fbdbb29-8ebf-4404-9f28-6024db8e148b____2X1.png', '{"EN-US": "<p>TESTINSTANTREC</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:37:05.000000', '2025-01-27 16:40:48.000000', 16, null, 0, 1, null, 'recurring', null, null, null, null, false, false);
--     --     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1529, 'TESTCASINOSPORTS', 50, true, '2025-01-27 16:41:00.000000', '2026-08-29 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TESTCASINO_SPORTS 10-500-250-50"}', 'tenants/bonus/3abe997e-86d2-4c5c-8d0b-4cf02f161238____2X1.png', '{"EN-US": "<p>TESTCASINO_SPORTS&nbsp;</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:40:48.000000', '2025-01-27 16:46:00.000000', 16, null, 0, 1, 'none', 'null', null, null, null, null, false, false);
--     --     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1530, 'TESTCASINOSPORTS1', 50, true, '2025-01-27 17:46:00.000000', '2026-01-16 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TEST-500-1000-250-50"}', 'tenants/bonus/f3cddf2a-40ae-4174-a672-ee88385a7370____2X1.png', '{"EN-US": "<p>TEST</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:46:00.000000', '2025-01-27 16:46:01.000000', 16, null, 0, 1, 'none', null, null, null, null, null, true, false);

--     --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (913, 10, 250, null, null, null, 1527, '2025-01-27 16:32:19.000000', '2025-01-27 16:32:19.000000', 500, null, 0, 'instant', null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--     --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (914, 10, 250, null, null, null, 1528, '2025-01-27 16:37:07.000000', '2025-01-27 16:37:07.000000', 500, null, 0, 'recurring', 'once_a_day', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--     --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (915, 10, 250, 4, 300, 5, 1529, '2025-01-27 16:40:50.000000', '2025-01-27 16:42:48.000000', 500, 1, 0, 'null', 'null', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--     --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (916, 500, 250, 3, 150, 2, 1530, '2025-01-27 16:46:01.000000', '2025-01-27 16:46:01.000000', 1000, 1, 0, null, null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);

         


--     --     delete from public.user_bonus_queue where user_id = 16754;

--     --     -- ORDER
--     --     -- 1493	- LOSING -1                                 -NO
--     --         -- 1530    - DEPOSIT - 500-1000-250-50              -NO
--     --     -- 1486	- LOSING -2                                 -NO
--     --         -- 1527    - DEPOSIT - 50-500-250-50 -INSTANT       -YES
--     --     -- 1483    - LOSING -3                              -NO
--     --         -- 1529    - DEPOSIT - 10-500-250-50                -NO
--     --     -- 1528    - DEPOSIT - 50-500-250-50 -INSTANT REC   -NO

--     --     -- AMOUNT - 250 - SO IN


--     --     --  ORDER OF INSERT[1493, 1530, 1486,1529, 1527, 1483, 1529]
--     --     -- No Change in the order
--     --     DO $$
--     --     BEGIN
--     --         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     --         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1530, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     --         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1486, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     --         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1527, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     --         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1483, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     --         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1529, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     --         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1528, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     --     END $$;

--     --     INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id)
--     --         VALUES ('bonus', 0, '[{"user": {"id": "16754","Wallet": {    "id": "18545",    "amount": 11185.400000000001,    "currencyId": "2",   "nonCashAmount": 0},"tenantId": "16","userName": "prashanth","exchangeRate": "90.00137","withdrawWagerAllowed": false},"amount": 250,"wallet": {"id": "18545","amount": 11435.400000000001,"ownerId": "16754","primary": false,"createdAt": "2025-01-10T05:12:04.000Z","ownerType": "User","updatedAt": "2025-01-23T08:20:07.046Z","currencyId": "2","nonCashAmount": 0,"withdrawalAmount": 0},"bonusType": "deposit","transactionIds": ["390173"],"paymentProviders": 3,"depositTransactionId": "390173"}]', '2025-01-23 08:20:07', '2025-01-23 08:20:07', null);
-- -- ****************************






-- -- ***************************************

-- -- CASE 3: ORDER  DEPOSIT BONUS ORDER

--     delete from user_bonus where  bonus_id in (1530, 1529, 1528, 1527, 1529, 589, 754);
--     delete from deposit_bonus_settings where deposit_bonus_settings.bonus_id in  (1530, 1529, 1528, 1527);
--     delete from bonus where id in  (1530, 1529, 1528, 1527);
--     delete  from user_bonus_queue where user_id =16754;
--     delete from queue_logs where status in ( 0, 1, 2, 3);
--     update user_bonus set status ='claimed' where user_id = 16754;
--     delete from transactions where id in (390206, 390173, 390215);

--     INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390215, 'User', 16754, null, 18545, null, 2, 90.00137, 125, 0, 0, 875, 1000, 'success', 'The Deposit bonus has been successfully claimed and was activated by the player on 2025-01-27T10:59:25.472Z.', '2025-01-27 10:59:25.507000', '2025-01-27 10:59:25.507000', 16, null, '1737975565500', 12, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, null, 'manual', '{"INR":125,"EUR":1.3889}', null, null, null, null);

--     INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390206, 'User', 16754, null, 18545, null, 2, 90.00137, 350, 0, 0, 11435.400000000001, 11785.400000000001, 'success', 'Deposit Request', '2025-01-23 09:05:09.552000', '2025-01-23 09:05:09.552000', 16, 'E20281E4-B630-4A08-80FC-57B22FE51176', '1737623109544', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'E20281E4-B630-4A08-80FC-57B22FE51176', 'UPI', '{"INR":350,"EUR":3.8888}', 3, null, null, null);

--     INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390173, 'User', 16754, null, 18545, null, 2, 90.00137, 250, 0, 0, 11185.400000000001, 11435.400000000001, 'success', 'Deposit Request', '2025-01-23 08:20:07.051000', '2025-01-23 08:20:07.051000', 16, 'CF87183C-0564-4849-B302-B80D753A44D0', '1737620407042', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'CF87183C-0564-4849-B302-B80D753A44D0', 'UPI', '{"INR":250,"EUR":2.7777}', 3, null, null, null);


--     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1527, 'TESTINSTANT', 50, true, '2025-01-27 16:33:00.000000', '2026-07-23 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TEST_INSTANT"}', 'tenants/bonus/dbd2324c-ec9a-46e4-a1f8-5cdaa52d3b94____2X1.png', '{"EN-US": "<p>TEST_INSTANT</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:32:17.000000', '2025-01-27 16:32:19.000000', 16, null, 0, 1, null, 'instant', null, null, null, null, false, false);
--     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1528, 'TESTINSTANTREC', 50, true, '2025-07-27 16:37:00.000000', '2026-09-25 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TESTINSTANTREC"}', 'tenants/bonus/2fbdbb29-8ebf-4404-9f28-6024db8e148b____2X1.png', '{"EN-US": "<p>TESTINSTANTREC</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:37:05.000000', '2025-01-27 16:40:48.000000', 16, null, 0, 1, null, 'recurring', null, null, null, null, false, false);
--     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1529, 'TESTCASINOSPORTS', 50, true, '2025-01-27 16:41:00.000000', '2026-08-29 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TESTCASINO_SPORTS 10-500-250-50"}', 'tenants/bonus/3abe997e-86d2-4c5c-8d0b-4cf02f161238____2X1.png', '{"EN-US": "<p>TESTCASINO_SPORTS&nbsp;</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:40:48.000000', '2025-01-27 16:46:00.000000', 16, null, 0, 1, 'none', 'null', null, null, null, null, false, false);
--     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1530, 'TESTCASINOSPORTS1', 50, true, '2025-01-27 17:46:00.000000', '2026-01-16 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TEST-500-1000-250-50"}', 'tenants/bonus/f3cddf2a-40ae-4174-a672-ee88385a7370____2X1.png', '{"EN-US": "<p>TEST</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:46:00.000000', '2025-01-27 16:46:01.000000', 16, null, 0, 1, 'none', null, null, null, null, null, true, false);


--     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (913, 10, 250, null, null, null, 1527, '2025-01-27 16:32:19.000000', '2025-01-27 16:32:19.000000', 500, null, 0, 'instant', null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (914, 10, 250, null, null, null, 1528, '2025-01-27 16:37:07.000000', '2025-01-27 16:37:07.000000', 500, null, 0, 'recurring', 'once_a_day', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (915, 10, 250, 4, 300, 5, 1529, '2025-01-27 16:40:50.000000', '2025-01-27 16:42:48.000000', 500, 1, 0, 'null', 'null', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (916, 500, 250, 3, 150, 2, 1530, '2025-01-27 16:46:01.000000', '2025-01-27 16:46:01.000000', 1000, 1, 0, null, null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);

--     delete from public.user_bonus_queue where user_id = 16754;

--     -- ORDER
--     -- 1493	- LOSING -1                                 -NO
--         -- 1530    - DEPOSIT - 500-1000-250-50              -NO
--     -- 1486	- LOSING -2                                 -NO
--         -- 1529    - DEPOSIT - 10-500-250-50               -YES
--         -- 1527    - DEPOSIT - 50-500-250-50 -INSTANT       -NO
--     -- 1483    - LOSING -3                              -NO
--     -- 1528    - DEPOSIT - 50-500-250-50 -INSTANT REC   -NO

--     -- AMOUNT - 250 - SO IN [1493, 1529, 1486, 1530, 1527, 1483,1528]


--     --  ORDER OF INSERT[1493, 1530, 1486, 1527, 1483, 1529, 1528]

--     DO $$
--     BEGIN
--         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1530, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1486, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1529, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1527, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1483, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--         INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, remaining_rollover) VALUES (16754, 1528, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, null);
--     END $$;

-- -- 589
-- INSERT INTO public.user_bonus ( status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES ( 'active', 0, 0, 16754, 754, 'deposit', '2024-02-01 23:59:59.000000', null, '2024-02-01 13:19:13.230000', '2024-02-02 00:00:02.000000', null, null, null);


-- -- 1529


--     INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id)
--     VALUES ('bonus', 0, '[{"user": {"id": "16754","Wallet": {    "id": "18545",    "amount": 11185.400000000001,    "currencyId": "2",   "nonCashAmount": 0},"tenantId": "16","userName": "prashanth","exchangeRate": "90.00137","withdrawWagerAllowed": false},"amount": 250,"wallet": {"id": "18545","amount": 11435.400000000001,"ownerId": "16754","primary": false,"createdAt": "2025-01-10T05:12:04.000Z","ownerType": "User","updatedAt": "2025-01-23T08:20:07.046Z","currencyId": "2","nonCashAmount": 0,"withdrawalAmount": 0},"bonusType": "deposit","transactionIds": ["390206"],"paymentProviders": 3,"depositTransactionId": "390206"}]', '2025-01-23 08:20:07', '2025-01-23 08:20:07', null);

--     --  ORDER
--     -- 1493	- LOSING -1                                 -NO
--     -- 1529 - DEPOSIT - 10-500-250-50                   -NO
--     -- 1486	- LOSING -2                                 -NO
--     --     1530    - DEPOSIT - 500-1000-250-50              -YES
--     --     1527    - DEPOSIT - 50-500-250-50 -INSTANT       -NO
--     -- 1483    - LOSING -3                              -NO
--     -- 1528    - DEPOSIT - 50-500-250-50 -INSTANT REC   -NO

--     -- AMOUNT - 600 - SO



--     -- -- AMOUNT - 600 - SO INSTANT
--     -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id)
--     -- VALUES ('bonus', 0, '[{"user": {"id": "16754","Wallet": {    "id": "18545",    "amount": 11185.400000000001,    "currencyId": "2",   "nonCashAmount": 0},"tenantId": "16","userName": "prashanth","exchangeRate": "90.00137","withdrawWagerAllowed": false},"amount": 250,"wallet": {"id": "18545","amount": 11435.400000000001,"ownerId": "16754","primary": false,"createdAt": "2025-01-10T05:12:04.000Z","ownerType": "User","updatedAt": "2025-01-23T08:20:07.046Z","currencyId": "2","nonCashAmount": 0,"withdrawalAmount": 0},"bonusType": "deposit","transactionIds": ["390215"],"paymentProviders": 3,"depositTransactionId": "390215"}]', '2025-01-23 08:20:07', '2025-01-23 08:20:07', null);

-- -- ****************************










-- -- ***************************************
-- -- ROLLOVER  case 4:
--             -- delete from user_bonus where  bonus_id in (1530, 1529, 1528, 1527);
--             -- delete from deposit_bonus_settings where deposit_bonus_settings.bonus_id in  (1530, 1529, 1528, 1527);
--             -- delete from bonus where id in  (1530, 1529, 1528, 1527);
--             -- delete  from user_bonus_queue where user_id =16754;
--             -- delete from queue_logs where status in ( 0, 1, 2, 3);
--             -- update user_bonus set status ='claimed' where user_id = 16754;
--             -- delete from transactions where id in (390206, 390173, 390215);
--             -- update user_bonus set status ='claimed' where user_id = 16754;
--             -- delete from user_bonus where id in (43893);

--             --  INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390215, 'User', 16754, null, 18545, null, 2, 90.00137, 125, 0, 0, 875, 1000, 'success', 'The Deposit bonus has been successfully claimed and was activated by the player on 2025-01-27T10:59:25.472Z.', '2025-01-27 10:59:25.507000', '2025-01-27 10:59:25.507000', 16, null, '1737975565500', 12, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, null, 'manual', '{"INR":125,"EUR":1.3889}', null, null, null, null);

--             -- INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390206, 'User', 16754, null, 18545, null, 2, 90.00137, 350, 0, 0, 11435.400000000001, 11785.400000000001, 'success', 'Deposit Request', '2025-01-23 09:05:09.552000', '2025-01-23 09:05:09.552000', 16, 'E20281E4-B630-4A08-80FC-57B22FE51176', '1737623109544', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'E20281E4-B630-4A08-80FC-57B22FE51176', 'UPI', '{"INR":350,"EUR":3.8888}', 3, null, null, null);

--             -- INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390173, 'User', 16754, null, 18545, null, 2, 90.00137, 250, 0, 0, 11185.400000000001, 11435.400000000001, 'success', 'Deposit Request', '2025-01-23 08:20:07.051000', '2025-01-23 08:20:07.051000', 16, 'CF87183C-0564-4849-B302-B80D753A44D0', '1737620407042', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'CF87183C-0564-4849-B302-B80D753A44D0', 'UPI', '{"INR":250,"EUR":2.7777}', 3, null, null, null);


--             -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1527, 'TESTINSTANT', 50, true, '2025-01-27 16:33:00.000000', '2026-07-23 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TEST_INSTANT"}', 'tenants/bonus/dbd2324c-ec9a-46e4-a1f8-5cdaa52d3b94____2X1.png', '{"EN-US": "<p>TEST_INSTANT</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:32:17.000000', '2025-01-27 16:32:19.000000', 16, null, 0, 1, null, 'instant', null, null, null, null, false, false);
--             -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1528, 'TESTINSTANTREC', 50, true, '2025-07-27 16:37:00.000000', '2026-09-25 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TESTINSTANTREC"}', 'tenants/bonus/2fbdbb29-8ebf-4404-9f28-6024db8e148b____2X1.png', '{"EN-US": "<p>TESTINSTANTREC</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:37:05.000000', '2025-01-27 16:40:48.000000', 16, null, 0, 1, null, 'recurring', null, null, null, null, false, false);
--             -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1529, 'TESTCASINOSPORTS', 50, true, '2025-01-27 16:41:00.000000', '2026-08-29 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TESTCASINO_SPORTS 10-500-250-50"}', 'tenants/bonus/3abe997e-86d2-4c5c-8d0b-4cf02f161238____2X1.png', '{"EN-US": "<p>TESTCASINO_SPORTS&nbsp;</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:40:48.000000', '2025-01-27 16:46:00.000000', 16, null, 0, 1, 'none', 'null', null, null, null, null, false, false);
--             -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1530, 'TESTCASINOSPORTS1', 50, true, '2025-01-27 17:46:00.000000', '2026-01-16 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TEST-500-1000-250-50"}', 'tenants/bonus/f3cddf2a-40ae-4174-a672-ee88385a7370____2X1.png', '{"EN-US": "<p>TEST</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:46:00.000000', '2025-01-27 16:46:01.000000', 16, null, 0, 1, 'none', null, null, null, null, null, true, false);


--             -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (913, 10, 250, null, null, null, 1527, '2025-01-27 16:32:19.000000', '2025-01-27 16:32:19.000000', 500, null, 0, 'instant', null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--             -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (914, 10, 250, null, null, null, 1528, '2025-01-27 16:37:07.000000', '2025-01-27 16:37:07.000000', 500, null, 0, 'recurring', 'once_a_day', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--             -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (915, 10, 250, 4, 300, 5, 1529, '2025-01-27 16:40:50.000000', '2025-01-27 16:42:48.000000', 500, 1, 0, 'null', 'null', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--             -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (916, 500, 250, 3, 150, 2, 1530, '2025-01-27 16:46:01.000000', '2025-01-27 16:46:01.000000', 1000, 1, 0, null, null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);

--             -- delete from public.user_bonus_queue where user_id = 16754;


--             -- INSERT INTO public.user_bonus (id, status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES (43893, 'active', 300, 20, 16754, 1473, 'deposit', '2025-01-31 18:29:00.000000', '2025-01-16 08:59:46.209000', '2025-01-16 08:33:42.509000', '2025-01-16 08:59:46.238000', null, 600, null);

--             -- DO $$
--             -- BEGIN
--             --    INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2422, 16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1932, null);
--             --     INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2425, 16754, 1529, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 125, 500, 4462, 1933, 500);
--             --     INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2424, 16754, 1486, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1934, null);
--             --     INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2423, 16754, 1530, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 250, 750, 4461, 1935, 750);
--             --     INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2426, 16754, 1527, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', null, null, null, 1936, null);
--             --     INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2427, 16754, 1483, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1937, null);
--             --     INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2428, 16754, 1528, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1938, null);
--             -- END $$;
        
--             -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id)
--             -- VALUES ('bonus', 0, '[
--             -- {
--             --     "userId": "16754",
--             --     "gameName": "190263",
--             --     "isCasino": false,
--             --     "tenantId": "16",
--             --     "bonusType": "deposit_rollover",
--             --     "providerId": "14",
--             --     "mircoService": "sports-aggregator",
--             --     "depositBonusType": [
--             --     "deposit",
--             --     "deposit_both"
--             --     ],
--             --     "rolloverAmountToDeduct": 100
--             -- }
--             -- ]', '2025-01-23 08:20:07', '2025-01-23 08:20:07', null);

--             -- 20 RollOver pending - Initated 100     -- 1529 -Target 500 = Result (500-80) 420
-- -- *************************************






-- -- ***************************************

-- -- ROLLOVER  case 5: clamimed
            
--         -- DELETE FROM public.user_bonus_queue WHERE user_id = 16754;

--         -- INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES ( 16754, 1492, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1665, null);
--         -- INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1490, 0, '2025-01-23 06:59:29', '2025-01-27 15:28:49', null, null, null, 1666, null);
--         -- INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1491, 0, '2025-01-23 06:59:38', '2025-01-27 15:28:49', null, null, null, 1664, null);
--         -- -- 
--         -- DELETE FROM public.user_bonus WHERE user_id = 16754;

--         -- INSERT INTO public.user_bonus ( status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES ('claimed', 300, 0, 16754, 1473, 'deposit', '2025-01-31 18:29:00.000000', '2025-01-16 08:59:46.209000', '2025-01-16 08:33:42.509000', '2025-01-16 08:59:46.238000', null, 600, null);
--         -- INSERT INTO public.user_bonus (status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES ('claimed', 125, 0, 16754, 1221, 'deposit', '2025-01-31 12:59:00.000000', null, '2025-01-24 15:04:14.000000', '2025-01-24 15:04:14.000000', null, 250, null);


--         -- delete from transactions where debit_transaction_id ='7B5C7721-4178-4CFF-A1E4-AF8774C442FC';
--         -- UPDATE public.deposit_requests SET user_id = 16754::bigint, status='opened' WHERE id = 1855::bigint returning id, user_id, order_id;
--         -- INSERT INTO "queue_logs" ( "type", "status", "ids", "created_at", "updated_at", "tenant_id") VALUES
--         -- ('payin_deposit_update_status',	0,	'[1855]',	'2025-01-27 11:25:22',	'2025-01-27 11:25:23',	NULL) returning type, status, ids;
--         -- UPDATE public.deposit_requests SET user_id = 16754::bigint WHERE id = 1855::bigint returning id, user_id, order_id;
--         -- UPDATE public.deposit_requests SET status = 'opened'::varchar(255) WHERE id = 1855::bigint-- delete from user_bonus where  bonus_id in (1530, 1529, 1528, 1527);
--         --     delete from deposit_bonus_settings where deposit_bonus_settings.bonus_id in  (1530, 1529, 1528, 1527);
--         --     delete from bonus where id in  (1530, 1529, 1528, 1527);
--         --     delete  from user_bonus_queue where user_id =16754;
--         --     delete from queue_logs where status in ( 0, 1, 2, 3);
--         --     update user_bonus set status ='claimed' where user_id = 16754;
--         --     delete from transactions where id in (390206, 390173, 390215);
--         --     update user_bonus set status ='claimed' where user_id = 16754;
--         --     delete from user_bonus where id in (43893);

--         --      INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390215, 'User', 16754, null, 18545, null, 2, 90.00137, 125, 0, 0, 875, 1000, 'success', 'The Deposit bonus has been successfully claimed and was activated by the player on 2025-01-27T10:59:25.472Z.', '2025-01-27 10:59:25.507000', '2025-01-27 10:59:25.507000', 16, null, '1737975565500', 12, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, null, 'manual', '{"INR":125,"EUR":1.3889}', null, null, null, null);

--         --     INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390206, 'User', 16754, null, 18545, null, 2, 90.00137, 350, 0, 0, 11435.400000000001, 11785.400000000001, 'success', 'Deposit Request', '2025-01-23 09:05:09.552000', '2025-01-23 09:05:09.552000', 16, 'E20281E4-B630-4A08-80FC-57B22FE51176', '1737623109544', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'E20281E4-B630-4A08-80FC-57B22FE51176', 'UPI', '{"INR":350,"EUR":3.8888}', 3, null, null, null);

--         --     INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390173, 'User', 16754, null, 18545, null, 2, 90.00137, 250, 0, 0, 11185.400000000001, 11435.400000000001, 'success', 'Deposit Request', '2025-01-23 08:20:07.051000', '2025-01-23 08:20:07.051000', 16, 'CF87183C-0564-4849-B302-B80D753A44D0', '1737620407042', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'CF87183C-0564-4849-B302-B80D753A44D0', 'UPI', '{"INR":250,"EUR":2.7777}', 3, null, null, null);


--         --     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1527, 'TESTINSTANT', 50, true, '2025-01-27 16:33:00.000000', '2026-07-23 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TEST_INSTANT"}', 'tenants/bonus/dbd2324c-ec9a-46e4-a1f8-5cdaa52d3b94____2X1.png', '{"EN-US": "<p>TEST_INSTANT</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:32:17.000000', '2025-01-27 16:32:19.000000', 16, null, 0, 1, null, 'instant', null, null, null, null, false, false);
--         --     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1528, 'TESTINSTANTREC', 50, true, '2025-07-27 16:37:00.000000', '2026-09-25 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TESTINSTANTREC"}', 'tenants/bonus/2fbdbb29-8ebf-4404-9f28-6024db8e148b____2X1.png', '{"EN-US": "<p>TESTINSTANTREC</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:37:05.000000', '2025-01-27 16:40:48.000000', 16, null, 0, 1, null, 'recurring', null, null, null, null, false, false);
--         --     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1529, 'TESTCASINOSPORTS', 50, true, '2025-01-27 16:41:00.000000', '2026-08-29 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TESTCASINO_SPORTS 10-500-250-50"}', 'tenants/bonus/3abe997e-86d2-4c5c-8d0b-4cf02f161238____2X1.png', '{"EN-US": "<p>TESTCASINO_SPORTS&nbsp;</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:40:48.000000', '2025-01-27 16:46:00.000000', 16, null, 0, 1, 'none', 'null', null, null, null, null, false, false);
--         --     INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1530, 'TESTCASINOSPORTS1', 50, true, '2025-01-27 17:46:00.000000', '2026-01-16 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TEST-500-1000-250-50"}', 'tenants/bonus/f3cddf2a-40ae-4174-a672-ee88385a7370____2X1.png', '{"EN-US": "<p>TEST</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:46:00.000000', '2025-01-27 16:46:01.000000', 16, null, 0, 1, 'none', null, null, null, null, null, true, false);


--         --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (913, 10, 250, null, null, null, 1527, '2025-01-27 16:32:19.000000', '2025-01-27 16:32:19.000000', 500, null, 0, 'instant', null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--         --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (914, 10, 250, null, null, null, 1528, '2025-01-27 16:37:07.000000', '2025-01-27 16:37:07.000000', 500, null, 0, 'recurring', 'once_a_day', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--         --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (915, 10, 250, 4, 300, 5, 1529, '2025-01-27 16:40:50.000000', '2025-01-27 16:42:48.000000', 500, 1, 0, 'null', 'null', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--         --     INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (916, 500, 250, 3, 150, 2, 1530, '2025-01-27 16:46:01.000000', '2025-01-27 16:46:01.000000', 1000, 1, 0, null, null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);

--         --     delete from public.user_bonus_queue where user_id = 16754;


--         --     INSERT INTO public.user_bonus (id, status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES (43893, 'active', 300, 20, 16754, 1473, 'deposit', '2025-01-31 18:29:00.000000', '2025-01-16 08:59:46.209000', '2025-01-16 08:33:42.509000', '2025-01-16 08:59:46.238000', null, 600, null);

--         --     DO $$
--         --     BEGIN
--         --        INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2422, 16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1932, null);
--         --         INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2425, 16754, 1529, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 125, 500, 4462, 1933, 500);
--         --         INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2424, 16754, 1486, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1934, null);
--         --         INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2423, 16754, 1530, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 250, 750, 4461, 1935, 750);
--         --         INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2426, 16754, 1527, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', null, null, null, 1936, null);
--         --         INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2427, 16754, 1483, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1937, null);
--         --         INSERT INTO public.user_bonus_queue (id, user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (2428, 16754, 1528, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1938, null);
--         --     END $$;
        
--         --     INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id)
--         --     VALUES ('bonus', 0, '[
--         --     {
--         --         "userId": "16754",
--         --         "gameName": "190263",
--         --         "isCasino": false,
--         --         "tenantId": "16",
--         --         "bonusType": "deposit_rollover",
--         --         "providerId": "14",
--         --         "mircoService": "sports-aggregator",
--         --         "depositBonusType": [
--         --         "deposit",
--         --         "deposit_both"
--         --         ],
--         --         "rolloverAmountToDeduct": 600
--         --     }
--         --     ]', '2025-01-23 08:20:07', '2025-01-23 08:20:07', null);

--             -- 20 RollOver pending - Initated 600     -- 1529 -Target 500 = Result (500-580) deduct - queue     insert of 580 
--             -- second queue max bonus for 580 is 300 so 500-300 = 200 Remaining
-- -- *************************************



-- -- ***************************************
-- -- ROLLOVER  case 6: 
--         -- delete from user_bonus where  bonus_id in (1530, 1529, 1528, 1527, 1494);
--         -- delete from deposit_bonus_settings where deposit_bonus_settings.bonus_id in  (1530, 1529, 1528, 1527, 1494);
--         -- delete from bonus where id in  (1530, 1529, 1528, 1527, 1494);
--         -- delete  from user_bonus_queue where user_id =16754;
--         -- delete from queue_logs where status in ( 0, 1, 2, 3);
--         -- update user_bonus set status ='claimed' where user_id = 16754;
--         -- delete from transactions where id in (390206, 390173, 390215, 390173);
--         -- update user_bonus set status ='claimed' where user_id = 16754;
--         -- delete from user_bonus where id in (43893);

--         --  INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390215, 'User', 16754, null, 18545, null, 2, 90.00137, 125, 0, 0, 875, 1000, 'success', 'The Deposit bonus has been successfully claimed and was activated by the player on 2025-01-27T10:59:25.472Z.', '2025-01-27 10:59:25.507000', '2025-01-27 10:59:25.507000', 16, null, '1737975565500', 12, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, null, 'manual', '{"INR":125,"EUR":1.3889}', null, null, null, null);

--         -- INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390206, 'User', 16754, null, 18545, null, 2, 90.00137, 350, 0, 0, 11435.400000000001, 11785.400000000001, 'success', 'Deposit Request', '2025-01-23 09:05:09.552000', '2025-01-23 09:05:09.552000', 16, 'E20281E4-B630-4A08-80FC-57B22FE51176', '1737623109544', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'E20281E4-B630-4A08-80FC-57B22FE51176', 'UPI', '{"INR":350,"EUR":3.8888}', 3, null, null, null);

--         -- INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390173, 'User', 16754, null, 18545, null, 2, 90.00137, 250, 0, 0, 11185.400000000001, 11435.400000000001, 'success', 'Deposit Request', '2025-01-23 08:20:07.051000', '2025-01-23 08:20:07.051000', 16, 'CF87183C-0564-4849-B302-B80D753A44D0', '1737620407042', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'CF87183C-0564-4849-B302-B80D753A44D0', 'UPI', '{"INR":250,"EUR":2.7777}', 3, null, null, null);


--         -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1527, 'TESTINSTANT', 50, true, '2025-01-27 16:33:00.000000', '2026-07-23 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TEST_INSTANT"}', 'tenants/bonus/dbd2324c-ec9a-46e4-a1f8-5cdaa52d3b94____2X1.png', '{"EN-US": "<p>TEST_INSTANT</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:32:17.000000', '2025-01-27 16:32:19.000000', 16, null, 0, 1, null, 'instant', null, null, null, null, false, false);
--         -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1528, 'TESTINSTANTREC', 50, true, '2025-07-27 16:37:00.000000', '2026-09-25 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TESTINSTANTREC"}', 'tenants/bonus/2fbdbb29-8ebf-4404-9f28-6024db8e148b____2X1.png', '{"EN-US": "<p>TESTINSTANTREC</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:37:05.000000', '2025-01-27 16:40:48.000000', 16, null, 0, 1, null, 'recurring', null, null, null, null, false, false);
--         -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1529, 'TESTCASINOSPORTS', 50, true, '2025-01-27 16:41:00.000000', '2026-08-29 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TESTCASINO_SPORTS 10-500-250-50"}', 'tenants/bonus/3abe997e-86d2-4c5c-8d0b-4cf02f161238____2X1.png', '{"EN-US": "<p>TESTCASINO_SPORTS&nbsp;</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:40:48.000000', '2025-01-27 16:46:00.000000', 16, null, 0, 1, 'none', 'null', null, null, null, null, false, false);
--         -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1530, 'TESTCASINOSPORTS1', 50, true, '2025-01-27 17:46:00.000000', '2026-01-16 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TEST-500-1000-250-50"}', 'tenants/bonus/f3cddf2a-40ae-4174-a672-ee88385a7370____2X1.png', '{"EN-US": "<p>TEST</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:46:00.000000', '2025-01-27 16:46:01.000000', 16, null, 0, 1, 'none', null, null, null, null, null, true, false);
--         -- INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1494, 'CASINO88', 50, true, '2025-01-20 07:48:00.000000', '2025-01-31 18:29:00.000000', 'deposit', 2, '{"EN-US": "Testing"}', 'tenants/bonus/e2a82d02-aa1f-46a4-92fe-230c4c1c4da4____social-media-image-sizes-1.png', '{"EN-US": "<p>Testing for the Screnndyrd</p>\n"}', '{3,4,5,6,7,8,9,10,0,1,2}', 1, '2025-01-20 07:48:03.000000', '2025-01-28 06:21:03.000000', 16, null, 1, 1, 'none', 'null', null, null, null, null, true, false);

--         -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (913, 10, 250, null, null, null, 1527, '2025-01-27 16:32:19.000000', '2025-01-27 16:32:19.000000', 500, null, 0, 'instant', null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--         -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (914, 10, 250, null, null, null, 1528, '2025-01-27 16:37:07.000000', '2025-01-27 16:37:07.000000', 500, null, 0, 'recurring', 'once_a_day', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--         -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (915, 10, 250, 4, 300, 5, 1529, '2025-01-27 16:40:50.000000', '2025-01-27 16:42:48.000000', 500, 1, 0, 'null', 'null', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--         -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (916, 500, 250, 3, 150, 2, 1530, '2025-01-27 16:46:01.000000', '2025-01-27 16:46:01.000000', 1000, 1, 0, null, null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
--         -- INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (879, 50, 300, 2, null, 5, 1494, '2025-01-20 07:48:03.000000', '2025-01-28 06:21:03.000000', 1000, 1, 1, 'null', 'null', 1, null, null, null, null, 1, null);


--         -- delete from public.user_bonus_queue where user_id = 16754;


--         -- INSERT INTO public.user_bonus (id, status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES (43893, 'active', 300, 20, 16754, 1402, 'deposit_sport', '2025-01-31 18:29:00.000000', '2025-01-16 08:59:46.209000', '2025-01-16 08:33:42.509000', '2025-01-16 08:59:46.238000', null, 600, null);

--         -- DO $$
--         -- BEGIN
--         --   INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', 250, 750, 1494, 1935, 750);
--         --    INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1932, null);
--         --     INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1529, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 125, 500, 4462, 1933, 500);
--         --     INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1486, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1934, null);
--         --     INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1530, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 250, 750, 4461, 1935, 750);
--         --     INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1527, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', null, null, null, 1936, null);
--         --     INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1483, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1937, null);
--         --     INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1528, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1938, null);
--         -- END $$;
    
--         -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id)
--         -- VALUES ('bonus', 0, '[{    "userId": "16754",    "gameName": "190263",    "isCasino": false,    "tenantId": "16",    "bonusType": "deposit_rollover",    "providerId": "14",    "mircoService": "sports-aggregator",    "depositBonusType": [ "deposit_both"    ],    "rolloverAmountToDeduct": 200}]', '2025-01-23 08:20:07', '2025-01-23 08:20:07', null);

--         -- 20 RollOver pending - QUEUE 500-200 = 300
-- -- *******************************









-- -- -- ***************************************
-- -- -- ROLLOVER  case 7: 
-- --             delete from user_bonus where  bonus_id in (1530, 1529, 1528, 1527, 1494);
-- --             delete from deposit_bonus_settings where deposit_bonus_settings.bonus_id in  (1530, 1529, 1528, 1527, 1494);
-- --             delete from bonus where id in  (1530, 1529, 1528, 1527, 1494);
-- --             delete  from user_bonus_queue where user_id =16754;
-- --             delete from queue_logs where status in ( 0, 1, 2, 3);
-- --             update user_bonus set status ='claimed' where user_id = 16754;
-- --             delete from transactions where id in (390206, 390173, 390215, 390173);
-- --             update user_bonus set status ='claimed' where user_id = 16754;
-- --             delete from user_bonus where id in (43893);

-- --              INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390215, 'User', 16754, null, 18545, null, 2, 90.00137, 125, 0, 0, 875, 1000, 'success', 'The Deposit bonus has been successfully claimed and was activated by the player on 2025-01-27T10:59:25.472Z.', '2025-01-27 10:59:25.507000', '2025-01-27 10:59:25.507000', 16, null, '1737975565500', 12, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, null, 'manual', '{"INR":125,"EUR":1.3889}', null, null, null, null);

-- --             INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390206, 'User', 16754, null, 18545, null, 2, 90.00137, 350, 0, 0, 11435.400000000001, 11785.400000000001, 'success', 'Deposit Request', '2025-01-23 09:05:09.552000', '2025-01-23 09:05:09.552000', 16, 'E20281E4-B630-4A08-80FC-57B22FE51176', '1737623109544', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'E20281E4-B630-4A08-80FC-57B22FE51176', 'UPI', '{"INR":350,"EUR":3.8888}', 3, null, null, null);

-- --             INSERT INTO public.transactions (id, actionee_type, actionee_id, source_wallet_id, target_wallet_id, source_currency_id, target_currency_id, conversion_rate, amount, source_before_balance, source_after_balance, target_before_balance, target_after_balance, status, comments, created_at, updated_at, tenant_id, transaction_id, timestamp, transaction_type, success, server_id, round_id, game_id, table_id, bet_type_id, seat_id, platform_id, error_code, error_description, return_reason, is_end_round, credit_index, debit_transaction_id, payment_method, other_currency_amount, payment_provider_id, cancel_transaction_id, meta_data, provider_id) VALUES (390173, 'User', 16754, null, 18545, null, 2, 90.00137, 250, 0, 0, 11185.400000000001, 11435.400000000001, 'success', 'Deposit Request', '2025-01-23 08:20:07.051000', '2025-01-23 08:20:07.051000', 16, 'CF87183C-0564-4849-B302-B80D753A44D0', '1737620407042', 3, true, null, null, null, null, null, null, null, 0, 'Completed Successfully', null, null, null, 'CF87183C-0564-4849-B302-B80D753A44D0', 'UPI', '{"INR":250,"EUR":2.7777}', 3, null, null, null);




-- --             INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1527, 'TESTINSTANT', 50, true, '2025-01-27 16:33:00.000000', '2026-07-23 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TEST_INSTANT"}', 'tenants/bonus/dbd2324c-ec9a-46e4-a1f8-5cdaa52d3b94____2X1.png', '{"EN-US": "<p>TEST_INSTANT</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:32:17.000000', '2025-01-27 16:32:19.000000', 16, null, 0, 1, null, 'instant', null, null, null, null, false, false);
-- --             INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1528, 'TESTINSTANTREC', 50, true, '2025-07-27 16:37:00.000000', '2026-09-25 18:29:00.000000', 'deposit_instant', 2, '{"EN-US": "TESTINSTANTREC"}', 'tenants/bonus/2fbdbb29-8ebf-4404-9f28-6024db8e148b____2X1.png', '{"EN-US": "<p>TESTINSTANTREC</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:37:05.000000', '2025-01-27 16:40:48.000000', 16, null, 0, 1, null, 'recurring', null, null, null, null, false, false);
-- --             INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1529, 'TESTCASINOSPORTS', 50, true, '2025-01-27 16:41:00.000000', '2026-08-29 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TESTCASINO_SPORTS 10-500-250-50"}', 'tenants/bonus/3abe997e-86d2-4c5c-8d0b-4cf02f161238____2X1.png', '{"EN-US": "<p>TESTCASINO_SPORTS&nbsp;</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:40:48.000000', '2025-01-27 16:46:00.000000', 16, null, 0, 1, 'none', 'null', null, null, null, null, false, false);
-- --             INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1530, 'TESTCASINOSPORTS1', 50, true, '2025-01-27 17:46:00.000000', '2026-01-16 18:29:00.000000', 'deposit_both', 2, '{"EN-US": "TEST-500-1000-250-50"}', 'tenants/bonus/f3cddf2a-40ae-4174-a672-ee88385a7370____2X1.png', '{"EN-US": "<p>TEST</p>\n"}', '{0,1,2,3,4,5,6,7,8,9,10}', 1, '2025-01-27 16:46:00.000000', '2025-01-27 16:46:01.000000', 16, null, 0, 1, 'none', null, null, null, null, null, true, false);
-- --             INSERT INTO public.bonus (id, code, percentage, enabled, valid_from, valid_upto, kind, currency_id, promotion_title, image, terms_and_conditions, vip_levels, usage_count, created_at, updated_at, tenant_id, flat, wallet_type, rollover_calculation_type, bonus_cancellation_type, deposit_bonus_type, burning_days, promo_codes, refer_type, refer_value, auto_activate_bonus, enable_past_date) VALUES (1494, 'CASINO88', 50, true, '2025-01-20 07:48:00.000000', '2025-01-31 18:29:00.000000', 'deposit', 2, '{"EN-US": "Testing"}', 'tenants/bonus/e2a82d02-aa1f-46a4-92fe-230c4c1c4da4____social-media-image-sizes-1.png', '{"EN-US": "<p>Testing for the Screnndyrd</p>\n"}', '{3,4,5,6,7,8,9,10,0,1,2}', 1, '2025-01-20 07:48:03.000000', '2025-01-28 06:21:03.000000', 16, null, 1, 1, 'none', 'null', null, null, null, null, true, false);



-- --             INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (913, 10, 250, null, null, null, 1527, '2025-01-27 16:32:19.000000', '2025-01-27 16:32:19.000000', 500, null, 0, 'instant', null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
-- --             INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (914, 10, 250, null, null, null, 1528, '2025-01-27 16:37:07.000000', '2025-01-27 16:37:07.000000', 500, null, 0, 'recurring', 'once_a_day', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
-- --             INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (915, 10, 250, 4, 300, 5, 1529, '2025-01-27 16:40:50.000000', '2025-01-27 16:42:48.000000', 500, 1, 0, 'null', 'null', 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
-- --             INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (916, 500, 250, 3, 150, 2, 1530, '2025-01-27 16:46:01.000000', '2025-01-27 16:46:01.000000', 1000, 1, 0, null, null, 1, '[3,7,1,2,5,77,76,75,72,104]', null, null, null, 1, null);
-- --             INSERT INTO public.deposit_bonus_settings (id, min_deposit, max_bonus, rollover_multiplier, max_rollover_per_bet, valid_for_days, bonus_id, created_at, updated_at, max_deposit, rollover_calculation_type, deposit_type, deposit_bonus_type, recurring_bonus_type, tier_type, allowed_payment_providers, custom_deposits, week_day, burning_days, burn_type, provider_details) VALUES (879, 50, 300, 2, null, 5, 1494, '2025-01-20 07:48:03.000000', '2025-01-28 06:21:03.000000', 1000, 1, 1, 'null', 'null', 1, null, null, null, null, 1, null);


-- --             delete from public.user_bonus_queue where user_id = 16754;


-- --             INSERT INTO public.user_bonus (id, status, bonus_amount, rollover_balance, user_id, bonus_id, kind, expires_at, claimed_at, created_at, updated_at, transaction_id, initial_rollover_balance, burning_trxn_id) VALUES (43893, 'active', 300, 20, 16754, 1402, 'deposit_sport', '2025-01-31 18:29:00.000000', '2025-01-16 08:59:46.209000', '2025-01-16 08:33:42.509000', '2025-01-16 08:59:46.238000', null, 600, null);

-- --             DO $$
-- --             BEGIN
-- --               INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', 250, 750, 1494, 1935, 750);
-- --                INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1493, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1932, null);
-- --                 INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1529, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 125, 100, 4462, 1933, 100);
-- --                 INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1486, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1934, null);
-- --                 INSERT INTO public.user_bonus_queue ( user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1530, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', 250, 750, 4461, 1935, 750);
-- --                 INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1527, 0, '2025-01-23 06:59:21', '2025-01-27 19:54:06', null, null, null, 1936, null);
-- --                 INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1483, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1937, null);
-- --                 INSERT INTO public.user_bonus_queue (user_id, bonus_id, status, created_at, updated_at, bonus_amount, rollover_target, deposit_id, ordering, remaining_rollover) VALUES (16754, 1528, 0, '2025-01-23 06:59:21', '2025-01-27 15:28:49', null, null, null, 1938, null);
-- --             END $$;
        
-- --             INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id)
-- --             VALUES ('bonus', 0, '[{    "userId": "16754",    "gameName": "190263",    "isCasino": false,    "tenantId": "16",    "bonusType": "deposit_rollover",    "providerId": "14",    "mircoService": "sports-aggregator",    "depositBonusType": [ "deposit_both"    ],    "rolloverAmountToDeduct": 200}]', '2025-01-23 08:20:07', '2025-01-23 08:20:07', null);
-- --             -- 20 pending - but sports
-- --             -- 0  completed 200-100 = 100
-- --             -- 750-100 = 650 


-- -- -- ***************************************

-- -- CUREENCY TEST CASES

-- -- -- ***************************************



-- -- UPDATE public.deposit_requests SET user_id = 1314, order_id = null, status = 'opened', ledger_id = null, data = '', created_at = '2024-08-13 12:24:07', updated_at = '2025-01-16 06:55:14', payment_provider_id = null, utr_number = 'dgd444w', transaction_receipt = 'tenants/1/user/1314/abccf06b-8158-4707-894b-33f419802918.jpeg', amount = 555, deposit_type = 'manual', bank_details = null, remark = null, tenant_id = 1, action_id = 1, action_type = 'AdminUser', session_id = null, tracking_id = null, payment_initiate = false, country_code = 'IN', currency_conversion = null, requested_currency_code = null, requested_amount = 555, verify_status = 'verified', old_amount = null, comment = null, checker_data = '{"id": 1, "email": "<EMAIL>", "remark": null, "user_name": "ben", "time_stamp": "2025-01-16T06:55:14.078429Z"}', maker_data = null, manual_deposit_type = 1, user_remark = null WHERE id = 2622;
-- -- UPDATE public.deposit_requests SET user_id = 1325, order_id = null, status = 'opened', ledger_id = null, data = '', created_at = '2024-08-21 12:10:25', updated_at = '2025-01-16 06:55:02', payment_provider_id = null, utr_number = '123455423355k', transaction_receipt = 'tenants/1/user/1325/eee214ff-1b7d-496d-9755-eba03a1bb2c8.png', amount = 150, deposit_type = 'manual', bank_details = null, remark = null, tenant_id = 1, action_id = 1, action_type = 'AdminUser', session_id = null, tracking_id = null, payment_initiate = false, country_code = 'IN', currency_conversion = null, requested_currency_code = null, requested_amount = 150, verify_status = 'verified', old_amount = null, comment = null, checker_data = '{"id": 1, "email": "<EMAIL>", "remark": null, "user_name": "ben", "time_stamp": "2025-01-16T06:55:02.682326Z"}', maker_data = null, manual_deposit_type = 1, user_remark = null WHERE id = 2672;
-- -- UPDATE public.deposit_requests SET user_id = 783, order_id = null, status = 'opened', ledger_id = null, data = '', created_at = '2024-11-15 11:50:27', updated_at = '2025-01-20 09:05:17', payment_provider_id = null, utr_number = 'ABC8766758486577', transaction_receipt = 'tenants/1/user/783/407ed542-b835-4c24-a202-b76811c81965.png', amount = 10, deposit_type = 'manual', bank_details = null, remark = null, tenant_id = 1, action_id = 1, action_type = 'AdminUser', session_id = null, tracking_id = null, payment_initiate = false, country_code = 'IN', currency_conversion = null, requested_currency_code = null, requested_amount = 100, verify_status = 'verified', old_amount = 100, comment = null, checker_data = '{"id": 1, "email": "<EMAIL>", "remark": null, "user_name": "ben", "time_stamp": "2025-01-16T06:54:51.917541Z"}', maker_data = '{"id": 1458, "email": "<EMAIL>", "remark": null, "user_name": "AadityaSubAdmin22", "time_stamp": "2025-01-20T09:05:17.811718Z"}', manual_deposit_type = 1, user_remark = 'vjhv' WHERE id = 3718;



-- -- -- ***************************************

-- -- -- locking in queue autoactivation bonus 

-- -- delete from user_bonus where  status = 'active';
-- -- delete from user_bonus_queue where status in (0,1,2);
-- -- delete from queue_logs where status in (0,1,2);

-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ( 'bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs ( type, status, ids, created_at, updated_at, tenant_id) VALUES ( 'bonus', 0, '[{"bonusId": 1525, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1525, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs ( type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ( 'bonus', 0, '[{"bonusId": 1525, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs ( type, status, ids, created_at, updated_at, tenant_id) VALUES ( 'bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1525, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs ( type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1525, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ( 'bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs ( type, status, ids, created_at, updated_at, tenant_id) VALUES ( 'bonus', 0, '[{"bonusId": 1525, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1525, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);
-- -- INSERT INTO public.queue_logs ( type, status, ids, created_at, updated_at, tenant_id) VALUES ('bonus', 0, '[{"bonusId": 1472, "timezone": "Asia/Kolkata", "bonusType": "auto_bonus_activate"}]', '2025-01-30 06:44:51', '2025-01-30 06:45:00', 1);

-- -- -- 

-- -- -- ***************************************
-- -- queue_logs
-- -- delete from user_bonus where status = 'active';
-- -- INSERT INTO queue_logs (type, status, ids, created_at, updated_at, tenant_id) VALUES ( 'bonus', 0, '[{"type": "auto", "bonusId": 1551, "timezone": "Asia/Colombo", "bonusType": "auto_bonus_activate"}]', '2025-01-31 13:19:53', '2025-01-31 13:20:01', 54);

-- -- -- ***************************************   


-- -- UPDATE public.deposit_requests SET user_id = 16943, order_id = null, status = 'opened', ledger_id = null, data = '', created_at = '2025-02-04 14:11:17', updated_at = '2025-02-04 14:14:58', payment_provider_id = null, utr_number = '****************', transaction_receipt = 'tenants/21/user/16943/b04a8b81-5661-4cfe-9320-eb9bcaf4da91.png', amount = 250, deposit_type = 'manual', bank_details = null, remark = 'Testing', tenant_id = 21, action_id = 279, action_type = 'AdminUser', session_id = null, tracking_id = '082f5661-1918-4c98-b57d-e20e8895ffc6', payment_initiate = false, country_code = 'IN', currency_conversion = 1.00000, requested_currency_code = 'INR', requested_amount = 250, verify_status = 'verified', old_amount = null, comment = null, checker_data = '{"id": 279, "email": "<EMAIL>", "remark": "Testing", "user_name": "Chelsea_Casey", "time_stamp": "2025-02-04T14:11:35.717936Z"}', maker_data = '{"id": "279", "email": "<EMAIL>", "remark": "Testing", "user_name": "Chelsea_Casey", "time_stamp": "2025-02-04T14:14:57.501Z"}', manual_deposit_type = 1, user_remark = 'Testing' WHERE id = 4569;

-- -- UPDATE public.deposit_withdraw_job SET type = 6, request_object = '{"1": {"ID": "4569", "Remark": "Testing", "Status": "approved", "Username": "timothy", "Agent Name": "Chelsea_Casey", "Actioned At": "04-02-2025 14:11:35", "User Remark": "Testing", "Country Code": "IN", "Deposit Type": "Manual", "Requested At": "04-02-2025 14:11:17", "Credit Amount": "250", "Exchange Rate": "N/A", "Payment Gateway": "N/A", "Requested Amount": "250", "Transaction Receipt": "tenants/21/user/16943/b04a8b81-5661-4cfe-9320-eb9bcaf4da91.png", "UTR Number/Transaction Id": "****************"}}', status = 1, created_at = '2025-02-04 14:14:56', updated_at = '2025-02-04 14:14:58', created_by = 279, tenant_id = 21, csv = true WHERE id = 1723;


-- -- INSERT INTO "queue_logs" ( "type", "status", "ids", "created_at", "updated_at", "tenant_id") VALUES
-- -- (	'bulk_deposit_request',	1,	'[1723]',	'2025-02-04 14:14:56',	'2025-02-04 14:14:57',	21);




-- menu Items
delete from menu_items
where id in (
    select id
    from (
        select id, row_number() over (partition by page_menu_id, casino_item_id order by id) as rn
        from menu_items
        where casino_item_id in (select id from casino_items where provider= '5099')
    ) t
    where rn > 1
);

-- casino_games
delete from casino_games
where id in (
    select id
    from (
        select id, row_number() over (partition by name order by id) as rn
        from casino_games where casino_provider_id = '5099'
    ) t
    where rn > 1
);

--casino_tables
delete from casino_tables
where id in (
    select id
    from (
       select id,table_id, row_number() over (partition by table_id order by id) as rn
        from casino_tables where provider_id = '5099'
    ) t
    where rn > 1
);



-- select game_id, array_agg(id) as ids
-- from (
--     select game_id, id, row_number() over (partition by game_id order by id) as rn
--     from casino_tables where provider_id = '5034'
-- ) t
-- where rn = 1
-- group by game_id;

-- select game_id, array_agg(id)
-- from  casino_tables where provider_id = '5099'
-- group by game_id;

-- delete from menu_items
-- where id in (
--     select id
--     from (
--         select id, row_number() over (partition by page_menu_id, casino_item_id order by id) as rn
--         from menu_items
--         where casino_item_id in (select id from casino_items where provider= '5034')
--     ) t
--     where rn > 1
-- );

-- select page_menu_id, casino_item_id, array_agg(id)
-- from menu_items
-- where casino_item_id in (select id from casino_items where provider= '5034')
-- group by page_menu_id, casino_item_id
-- having COUNT(menu_items.id) > 1;