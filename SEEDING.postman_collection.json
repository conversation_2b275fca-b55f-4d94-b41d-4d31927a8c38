{"info": {"_postman_id": "40bb4968-75e5-4794-ac9c-4641a757774e", "name": "SEEDING", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "37248951"}, "item": [{"name": "darwin", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/darwin", "host": ["{{baseUrl}}"], "path": ["darwin"]}, "description": " JobController.darwinGameUpdation\n casino"}, "response": []}, {"name": "pgsoft", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"tenantId\":0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/pgSoft", "host": ["{{baseUrl}}"], "path": ["pgSoft"]}, "description": "QUEUE: JobController.lotteryGamesUpdation"}, "response": []}, {"name": "funky", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"tenantId\":0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/funky", "host": ["{{baseUrl}}"], "path": ["funky"]}, "description": "QUEUE:  JobController.funkyGamesUpdation\nCasino"}, "response": []}, {"name": "whitecliff", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "url": {"raw": "{{baseUrl}}/whitecliff", "host": ["{{baseUrl}}"], "path": ["whitecliff"]}, "description": "QUEUE: JobController.whiteCliffGamesUpdation\n'Live Casino'; 'Virtual Sports'; 'casino'"}, "response": []}, {"name": "lottery777", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"tenantId\":0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/lottery777", "host": ["{{baseUrl}}"], "path": ["lottery777"]}, "description": "QUEUE: JobController.lotteryGamesUpdation"}, "response": []}, {"name": "<PERSON><PERSON><PERSON>", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "games-auth-token", "value": "{{game<PERSON><PERSON>Token}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/update-games?sheet_id={{ezugiSheet}}&tenant_ids=0&only_master_tenant=true", "host": ["{{baseUrl}}"], "path": ["update-games"], "query": [{"key": "sheet_id", "value": "{{ezugiSheet}}"}, {"key": "tenant_ids", "value": "0"}, {"key": "only_master_tenant", "value": "true"}]}, "description": "Copy from excel"}, "response": []}, {"name": "Evolution", "protocolProfileBehavior": {"disableBodyPruning": true}, "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "GET", "header": [{"key": "games-auth-token", "value": "{{game<PERSON><PERSON>Token}}", "type": "text"}], "body": {"mode": "raw", "raw": "", "options": {"raw": {"language": "json"}}}, "url": {"raw": "{{baseUrl}}/update-games?sheet_id={{evoltionSheet}}&tenant_ids=0&only_master_tenant=true", "host": ["{{baseUrl}}"], "path": ["update-games"], "query": [{"key": "sheet_id", "value": "{{evoltionSheet}}"}, {"key": "tenant_ids", "value": "0"}, {"key": "only_master_tenant", "value": "true"}]}, "description": "Copy from excel"}, "response": []}], "variable": [{"key": "baseUrl", "value": "", "type": "default"}, {"key": "ezugiSheet", "value": "", "type": "default"}, {"key": "evoltionSheet", "value": "", "type": "default"}, {"key": "gameAuthToken", "value": "", "type": "default"}]}