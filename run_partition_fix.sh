#!/bin/bash

# <PERSON>ript to fix the archive_transactions partition issue
# This script creates the missing partition for September 25, 2024

set -e  # Exit on any error

# Database connection parameters
DB_NAME="ezugimainplatform_staging"
DB_USER="postgres"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

echo "Starting partition fix for archive_transactions table..."

# Check if we're running in Docker or local environment
if command -v docker &> /dev/null && docker ps | grep -q "entrypoint_database_1"; then
    echo "Running in Docker environment..."
    
    # Execute the fix script in Docker
    docker exec -i entrypoint_database_1 psql -U "$DB_USER" -d "$DB_NAME" -f /fix_archive_transactions_partition.sql || {
        echo "Failed to execute partition fix in Docker. Trying to copy file first..."
        
        # Copy the fix script to Docker container
        docker cp "$SCRIPT_DIR/fix_archive_transactions_partition.sql" entrypoint_database_1:/fix_archive_transactions_partition.sql || {
            echo "Failed to copy fix script to Docker container"
            exit 1
        }
        
        # Execute the fix script
        docker exec -i entrypoint_database_1 psql -U "$DB_USER" -d "$DB_NAME" -f /fix_archive_transactions_partition.sql || {
            echo "Failed to execute partition fix in Docker"
            exit 1
        }
    }
    
elif command -v psql &> /dev/null; then
    echo "Running in local environment..."
    
    # Execute the fix script locally
    psql -U "$DB_USER" -d "$DB_NAME" -f "$SCRIPT_DIR/fix_archive_transactions_partition.sql" || {
        echo "Failed to execute partition fix locally"
        exit 1
    }
    
else
    echo "Error: Neither Docker nor psql command found. Cannot execute the fix."
    exit 1
fi

echo "Partition fix completed successfully!"

# Verify the partition was created
echo "Verifying partition creation..."

if command -v docker &> /dev/null && docker ps | grep -q "entrypoint_database_1"; then
    docker exec -i entrypoint_database_1 psql -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            'Partition created successfully' as status
        FROM pg_tables 
        WHERE tablename = 'archive_transactions_p20240925';"
else
    psql -U "$DB_USER" -d "$DB_NAME" -c "
        SELECT 
            schemaname,
            tablename,
            'Partition created successfully' as status
        FROM pg_tables 
        WHERE tablename = 'archive_transactions_p20240925';"
fi

echo "You can now retry your migration. The partition for September 25, 2024 has been created."
