#!/bin/bash

# Initialize variables
status=""
admin=""
docker_entrypoint_dir=""
queue_dir=""
admin_csb_dir=""
report_be_dir=""

# Source ENTRYPOINT_DIR from config.env
if [ -f "/home/<USER>/Desktop/migration/config.env" ]; then
    source "/home/<USER>/Desktop/migration/config.env"
    if [ -z "$ENTRYPOINT_DIR" ]; then
        echo "Error: ENTRYPOINT_DIR is not set in config.env."
        exit 1
    fi
    docker_entrypoint_dir="$ENTRYPOINT_DIR"
else
    echo "Error: config.env file not found."
    exit 1
fi

# Parse command line arguments
for arg in "$@"; do
    case $arg in
        --status=*)
            status="${arg#*=}"
            shift
            ;;
        --admin=*)
            admin="${arg#*=}"
            shift
            ;;
        --queue-dir=*)
            queue_dir="${arg#*=}"
            shift
            ;;
        --admin-csb-dir=*)
            admin_csb_dir="${arg#*=}"
            shift
            ;;
        --docker-entrypoint-dir=*)
            docker_entrypoint_dir="${arg#*=}"
            shift
            ;;
        --report-be-dir=*)
            report_be_dir="${arg#*=}"
            shift
            ;;
        *)
            echo "Unknown argument: $arg"
            exit 1
            ;;
    esac
done

# Validate and set directory variables
if [ -z "$docker_entrypoint_dir" ]; then
    docker_entrypoint_dir="$ENTRYPOINT_DIR"
    if [ -z "$docker_entrypoint_dir" ]; then
        echo "Error: Entrypoint directory is not set. Please provide --docker-entrypoint-dir or set ENTRYPOINT_DIR in config.env."
        exit 1
    fi
fi

if [ -z "$admin" ]; then
    admin="$ADMIN_DIR"
    if [ -z "$admin" ]; then
        echo "Error: Admin directory is not set. Please provide --admin or set ADMIN_DIR in config.env."
        exit 1
    fi
fi

if [ -z "$queue_dir" ]; then
    queue_dir="$QUEUE_DIR"
    if [ -z "$queue_dir" ]; then
        echo "Error: Queue directory is not set. Please provide --queue-dir or set QUEUE_DIR in config.env."
        exit 1
    fi
fi

if [ -z "$admin_csb_dir" ]; then
    admin_csb_dir="$ADMIN_CSB_DIR"
    if [ -z "$admin_csb_dir" ]; then
        echo "Error: Admin CSB directory is not set. Please provide --admin-csb-dir or set ADMIN_CSB_DIR in config.env."
        exit 1
    fi
fi

if [ -z "$report_be_dir" ]; then
    report_be_dir="$REPORT_BE_DIR"
    if [ -z "$report_be_dir" ]; then
        echo "Error: Report BE directory is not set. Please provide --report-be-dir or set REPORT_BE_DIR in config.env."
        exit 1
    fi
fi

# Main logic based on status
case "$status" in
    # "user-start")
    #     sudo service apache2 stop
    #     cd ~/Documents/project/entrypoint/ || exit 1
    #     make dirty-up
    #     ;;
    # "user-stop")
    #     cd ~/Documents/project/entrypoint/ || exit 1
    #     make down
    #     sudo service apache2 start
    #     ;;
    # "elastic-start")
    #     cd || exit 1
    #     docker-compose --file '/var/www/html/ezugi/admin-node/docker-compose.yml' --project-name 'admin-node' start
    #     cd /var/www/html/ezugi/admin-node/admin-node || exit 1
    #     npm run esmapplingsetting
    #     ;;
    # "reindex")
    #     cd || exit 1
    #     docker-compose --file '/var/www/html/ezugi/admin-node/docker-compose.yml' --project-name 'admin-node' start
    #     cd /var/www/html/ezugi/admin-node/admin-node || exit 1
    #     npm run esmapplingsetting
    #     xdg-open http://localhost/ezugi/admin-csb/backend/reindex-user
    #     xdg-open http://localhost/ezugi/admin-csb/backend/reindex-bet-transaction
    #     xdg-open http://localhost/ezugi/admin-csb/backend/reindex-audit-log
    #     xdg-open http://localhost/ezugi/admin-csb/backend/reindex-transaction
    #     ;;
    # "elastic-stop")
    #     cd || exit 1
    #     docker-compose --file '/var/www/html/ezugi/admin-node/docker-compose.yml' --project-name 'admin-node' stop
    #     ;;
    # "reindex_t")
    #     cd || exit 1
    #     docker-compose --file '/var/www/html/ezugi/admin-node/docker-compose.yml' --project-name 'admin-node' start
    #     cd /var/www/html/ezugi/admin-node/admin-node || exit 1
    #     npm run esmapplingsetting
    #     export PGPASSWORD='root'
    #     ids=$(psql -U postgres -d ezugimainplatform_staging -t -c "SELECT id FROM transactions ORDER BY id DESC LIMIT 50;")
    #     for id in $ids; do
    #         curl -X GET "http://localhost/ezugi/admin-csb/backend/reindex-transaction/$id"
    #     done
    #     unset PGPASSWORD
    #     ;;
    # "entrypoint")
    #     echo "Stopping entrypoint services list..."
    #     names=$(docker ps --filter "name=entrypoint" --format "{{.Names}}")
    #     for name in $names; do
    #         echo "Stopping $name..."
    #         docker stop "$name"
    #     done

    #     read -p "Do you want to start or stop entrypoint services? (start/stop): " action
    #     case "$action" in
    #         "start")
    #             read -p "Do you want to stop admin-csb services? (y/n): " stop_admin
    #             if [ "$stop_admin" = "y" ]; then
    #                 sudo service apache2 stop
    #                 pid=$(lsof -t -i:4200)
    #                 if [ -n "$pid" ]; then
    #                     sudo kill -9 "$pid"
    #                 else
    #                     echo "No process found on port 4200"
    #                 fi
    #             fi

    #             echo "Moving to entrypoint services directory: $docker_entrypoint_dir"
    #             if [ ! -d "$docker_entrypoint_dir" ]; then
    #                 echo "Error: Entrypoint directory not found: $docker_entrypoint_dir"
    #                 exit 1
    #             fi
    #             cd "$docker_entrypoint_dir" || exit 1

    #             read -p "Do you want to use the latest entrypoint or old entrypoint? (y/n): " latest_entrypoint
    #             if [ "$latest_entrypoint" = "y" ]; then
    #                 make down
    #                 cd ../user-backend-api && git pull
    #                 cd ../user-frontend && git pull
    #                 cd ../entrypoint || exit 1
    #                 make setup
    #                 make dirty-up
    #             else
    #                 docker-compose --file "$docker_entrypoint_dir/docker-compose.yml" --project-name 'entrypoint' up -d
    #             fi

    #             echo "Checking if entrypoint services are running..."
    #             entrypoint_status=$(docker ps --filter "name=entrypoint" --format "{{.Names}}")
    #             if [ -n "$entrypoint_status" ]; then
    #                 echo "Entrypoint services are running."
    #             else
    #                 echo "Entrypoint services are not running."
    #             fi
    #             ;;
    #         "stop")
    #             echo "Stopping entrypoint services..."
    #             names=$(docker ps --filter "name=entrypoint" --format "{{.Names}}")
    #             for name in $names; do
    #                 echo "Stopping $name..."
    #                 docker stop "$name"
    #             done
    #             echo "Stopped entrypoint services."

    #             read -p "Do you want to start admin-csb services? (y/n): " start_admin
    #             if [ "$start_admin" = "y" ]; then
    #                 sudo service apache2 start
    #             fi

    #             read -p "Do you want to start admin-frontend services? (y/n): " start_admin_frontend
    #             if [ "$start_admin_frontend" = "y" ]; then
    #                 echo "Starting admin frontend services in directory: $admin"
    #                 if [ ! -d "$admin" ]; then
    #                     echo "Error: Admin frontend directory not found: $admin"
    #                     exit 1
    #                 fi
    #                 cd "$admin" || exit 1
    #                 npm start
    #             fi
    #             ;;
    #         *)
    #             echo "Invalid action: $action"
    #             exit 1
    #             ;;
    #     esac
    #     ;;
    "run")
        echo "Select the project you want to run:"
        options=("csb" "admin" "Database" "reportBE" "entrypoint" "queue" "Kill:port" "swithCSBwithNginx" "Exit")
        select project in "${options[@]}"
        do
            case $project in
                "csb")
                    echo "You selected CSB project."
                    echo "Select the action:"
                    actions=("start" "stop" "openInVsCode" "openInPhpStorm" "Exit")
                    select action in "${actions[@]}"
                    do
                        case $action in
                            "start")
                                echo "Starting CSB project..."
                                docker stop entrypoint_nginx_1
                                sudo service apache2 start
                                break
                                ;;
                            "stop")
                                echo "Stopping CSB project..."
                                sudo service apache2 stop
                                break
                                ;;
                            "openInVsCode")
                                echo "Opening CSB project in VS Code..."
                                if [ ! -d "$admin_csb_dir" ]; then
                                    echo "Error: Admin frontend directory not found: $admin_csb_dir"
                                    exit 1
                                fi
                                cd "$admin_csb_dir" || exit 1
                                code .
                                break
                                ;;
                            "openInPhpStorm")
                                echo "Opening CSB project in PhpStorm..."
                                if [ ! -d "$admin_csb_dir" ]; then
                                    echo "Error: Admin frontend directory not found: $admin_csb_dir"
                                    exit 1
                                fi
                                cd "$admin_csb_dir" || exit 1
                                phpstorm .
                                break
                                ;;
                            "Exit")
                                echo "Exiting..."
                                exit 0
                                ;;
                            *)
                                echo "Invalid option, please choose a valid action."
                                ;;
                        esac
                    done
                    break
                    ;;
                "admin")
                    echo "You selected Admin project."
                    echo "Select the action:"
                    actions=("start" "stop" "openInVsCode" "Exit")
                    select action in "${actions[@]}"
                    do
                        case $action in
                            "start")
                                echo "Starting Admin project..."
                                if [ ! -d "$admin" ]; then
                                    echo "Error: Admin frontend directory not found: $admin"
                                    exit 1
                                fi
                                cd "$admin" || exit 1
                                npm start
                                break
                                ;;
                            "stop")
                                echo "Stopping Admin project..."
                                pid=$(lsof -t -i:4200)
                                if [ -n "$pid" ]; then
                                    sudo kill -9 "$pid"
                                else
                                    echo "No process found on port 4200"
                                fi
                                break
                                ;;
                            "openInVsCode")
                                echo "Opening Admin project in VS Code..."
                                if [ ! -d "$admin" ]; then
                                    echo "Error: Admin frontend directory not found: $admin"
                                    exit 1
                                fi
                                cd "$admin" || exit 1
                                code .
                                break
                                ;;
                            "Exit")
                                echo "Exiting..."
                                exit 0
                                ;;
                            *)
                                echo "Invalid option, please choose a valid action."
                                ;;
                        esac
                    done
                    break
                    ;;
                "entrypoint")
                    echo "You selected Entrypoint project."
                    echo "Select the action:"
                    actions=("start" "stop" "entrypoint:VSCode" "UserBackend" "UserFrontend"  "pullLatestUserFEandBE" "setup&Up" "Exit")
                    select action in "${actions[@]}"
                    do
                        case $action in
                            "start")
                                echo "Starting Entrypoint project..."
                                # echo "Starting Entrypoint project..."
                                # cd "$docker_entrypoint_dir" || exit 1
                                # docker-compose --file "$docker_entrypoint_dir/docker-compose.yml" --project-name 'entrypoint' up -d
                                # break
                                # ;;
                                # select from container or all
                                # get all containers
                                containers=$(docker ps -a  --format "{{.Names}}")
                                # add "all" to the list
                                containersList="all $containers"
                                select container in $containersList
                                do
                                    case $container in
                                        "all")
                                            echo "Starting all Entrypoint containers..."
                                            docker-compose --file "$docker_entrypoint_dir/docker-compose.yml" --project-name 'entrypoint' up -d
                                            break
                                            ;;
                                        *)
                                            echo "Starting Entrypoint container: $container..."
                                            docker start "$container"
                                            break
                                            ;;
                                    esac
                                done
                                break
                                ;;
                            "stop")
                                echo "Stopping Entrypoint project..."
                                # get all containers
                                containers=$(docker ps --filter "name=entrypoint" --format "{{.Names}}")
                                # add "all" to the list
                                containersList="all $containers"
                                select container in $containersList
                                do
                                    case $container in
                                        "all")
                                            echo "Stopping all Entrypoint containers..."
                                            docker-compose --file "$docker_entrypoint_dir/docker-compose.yml" --project-name 'entrypoint' stop
                                            break
                                            ;;
                                        *)
                                            echo "Stopping Entrypoint container: $container..."
                                            docker stop "$container"
                                            break
                                            ;;
                                    esac
                                done
                                break
                                ;;
                                
                            "entrypoint:VSCode")
                                echo "Opening Entrypoint project in VS Code..."
                                if [ ! -d "$docker_entrypoint_dir" ]; then
                                    echo "Error: Entrypoint directory not found: $docker_entrypoint_dir"
                                    exit 1
                                fi
                                cd "$docker_entrypoint_dir" || exit 1
                                code .
                                break
                                ;;
                            "UserBackend")
                                echo "Opening User Backend project in VS Code..."
                                if [ ! -d "$docker_entrypoint_dir" ]; then
                                    echo "Error: Entrypoint directory not found: $docker_entrypoint_dir"
                                    exit 1
                                fi
                                cd "$docker_entrypoint_dir" || exit 1
                                cd ../user-backend-api || exit 1
                                code .
                                break
                                ;;
                            "UserFrontend")
                                echo "Opening User Frontend project in VS Code..."
                                if [ ! -d "$docker_entrypoint_dir" ]; then
                                    echo "Error: Entrypoint directory not found: $docker_entrypoint_dir"
                                    exit 1
                                fi
                                cd "$docker_entrypoint_dir" || exit 1
                                cd ../user-frontend || exit 1
                                code .
                                break
                                ;;
                            "pullLatestUserFEandBE")
                                echo "Pulling latest User Frontend and Backend..."
                                if [ ! -d "$docker_entrypoint_dir" ]; then
                                    echo "Error: Entrypoint directory not found: $docker_entrypoint_dir"
                                    exit 1
                                fi
                                cd "$docker_entrypoint_dir" || exit 1
                                cd ../user-backend-api || exit 1
                                git pull
                                cd ../user-frontend || exit 1
                                git pull
                                break
                                ;;
                            "setup&Up")
                                echo "Setting up and starting Entrypoint project..."
                                if [ ! -d "$docker_entrypoint_dir" ]; then
                                    echo "Error: Entrypoint directory not found: $docker_entrypoint_dir"
                                    exit 1
                                fi
                                cd "$docker_entrypoint_dir" || exit 1
                                make set-up
                                make dirty-up
                                break
                                ;;
                            "Exit")
                                echo "Exiting..."
                                exit 0
                                ;;
                            *)
                                echo "Invalid option, please choose a valid action."
                                ;;
                        esac
                    done
                    break
                    ;;
                "queue")
                    echo "You selected Queue project."
                    echo "Select the action:"
                    actions=("destktop" "cron" "worker" "openInVsCode" "Exit")
                    select action in "${actions[@]}"
                    do
                        case $action in
                            "destktop")
                                echo "Starting Queue Desktop project..."
                                cd "$queue_dir" || exit 1
                                npm run start:dev
                                break
                                ;;
                            "cron")
                                echo "Starting Queue Cron project..."
                                cd "$queue_dir" || exit 1
                                npm run start:dev:cron
                                break
                                ;;
                            "worker")
                                echo "Starting Queue Worker project..."
                                cd "$queue_dir" || exit 1
                                npm run start:dev:workers
                                break
                                ;;
                            "openInVsCode")
                                echo "Opening Queue project in VS Code..."
                                if [ ! -d "$queue_dir" ]; then
                                    echo "Error: Queue directory not found: $queue_dir"
                                    exit 1
                                fi
                                cd "$queue_dir" || exit 1
                                code .
                                break
                                ;;
                            "Exit")
                                echo "Exiting..."
                                exit 0
                                ;;
                            *)
                                echo "Invalid option, please choose a valid action."
                                ;;
                        esac
                    done
                    break
                    ;;
                "Kill:port")
                    read -p "Enter the port number to kill: " port_number
                    pids=$(lsof -t -i:"$port_number")
                    if [ -n "$pids" ]; then
                        echo "Killing the following PIDs on port $port_number: $pids"
                        for pid in $pids; do
                            sudo kill -9 "$pid"
                            echo "Killed PID $pid"
                        done
                    else
                        echo "No process found on port $port_number"
                    fi
                    break
                    ;;
                "Database")
                    echo "You selected Database project."
                    echo "Select the action:"
                    actions=("LatestDBToLocal" "LatestDBToDocker" "LatestFunctionsToLocal" "LatestFunctionsToDocker" "dump" "Exit")
                    select action in "${actions[@]}"
                    do
                        case $action in
                            "LatestDBToLocal")
                                echo "Running LatestDBToLocal..."
                                make migration
                                break
                                ;;
                            "LatestDBToDocker")
                                echo "Running LatestDBToDocker..."
                                make docker-db
                                break
                                ;;
                            "LatestFunctionsToLocal")
                                echo "Running LatestFunctionsToLocal..."
                                make functions
                                break
                                ;;
                            "LatestFunctionsToDocker")
                                echo "Running LatestFunctionsToDocker..."
                                make docker-functions
                                break
                                ;;
                            "dump")
#!/bin/bash

echo "Running dump..."

# Ask for PostgreSQL password
read -s -p "Enter PostgreSQL password: " PGPASSWORD
export PGPASSWORD
echo

# Run the curl command
                        curl 'https://adminapi.lionplay.co/erwerqw_fgf_fjgh_recdfgsdf.php?pgsql=ezugi-main-platform-staging-rds.cluster-ro-c3adg1flmeri.us-east-1.rds.amazonaws.com&username=postgres&dump=' \
                        -H 'accept: text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7' \
                        -H 'accept-language: en-GB,en-US;q=0.9,en;q=0.8' \
                        -H 'cache-control: max-age=0' \
                        -H 'content-type: application/x-www-form-urlencoded' \
                        -H 'cookie: adminer_export=output%3Dgz%26format%3Dsql%26db_style%3DCREATE%26routines%3D1%26events%3D%26table_style%3DCREATE%26auto_increment%3D1%26triggers%3D1%26data_style%3DINSERT; adminer_key=4e9deb1eb0ed216965ed3535ed4fff59; adminer_sid=abbgkcj4ih1g0sttukkkqk90n7; adminer_permanent=cGdzcWw%3D-ZXp1Z2ktbWFpbi1wbGF0Zm9ybS1zdGFnaW5nLXJkcy5jbHVzdGVyLXJvLWMzYWRnMWZsbWVyaS51cy1lYXN0LTEucmRzLmFtYXpvbmF3cy5jb20%3D-cG9zdGdyZXM%3D-%3At71gWRrL0bQi7Fm0F1e%2BvBj7H6sP7BU5+cGdzcWw%3D-ZXp1Z2ktbWFpbi1wbGF0Zm9ybS1zdGFnaW5nLXJkcy5jbHVzdGVyLXJvLWMzYWRnMWZsbWVyaS51cy1lYXN0LTEucmRzLmFtYXpvbmF3cy5jb20%3D-cG9zdGdyZXM%3D-ZXp1Z2ltYWlucGxhdGZvcm1fc3RhZ2luZw%3D%3D%3Adgt5PA32urn7PZerUlORaiseVj0pKTXV' \
                        -H 'origin: https://adminapi.lionplay.co' \
                        -H 'priority: u=0, i' \
                        -H 'referer: https://adminapi.lionplay.co/erwerqw_fgf_fjgh_recdfgsdf.php?pgsql=ezugi-main-platform-staging-rds.cluster-ro-c3adg1flmeri.us-east-1.rds.amazonaws.com&username=postgres&dump=' \
                        -H 'sec-ch-ua: "Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"' \
                        -H 'sec-ch-ua-mobile: ?0' \
                        -H 'sec-ch-ua-platform: "Linux"' \
                        -H 'sec-fetch-dest: document' \
                        -H 'sec-fetch-mode: navigate' \
                        -H 'sec-fetch-site: same-origin' \
                        -H 'sec-fetch-user: ?1' \
                        -H 'upgrade-insecure-requests: 1' \
                        -H 'user-agent: Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/126.0.0.0 Safari/537.36' \
                        --data-raw 'output=gz&format=sql&db_style=CREATE&routines=1&table_style=CREATE&auto_increment=1&triggers=1&data_style=INSERT&token=748338%3A95987&databases%5B%5D=ezugimainplatform_staging' \
                        --output ezugimainplatform_staging.sql.gz

                                echo "Dump completed."
                                unset PGPASSWORD
                                break
                                ;;




                            "Exit")
                                echo "Exiting..."
                                exit 0
                                ;;
                            *)
                                echo "Invalid option, please choose a valid action."
                                ;;
                        esac
                    done
                    break
                    ;;
                "reportBE")
                    echo "You selected Report BE project."
                    echo "Select the action:"
                    actions=("start" "stop" "openInVsCode" "Exit")
                    select action in "${actions[@]}"
                    do
                        case $action in
                            "start")
                                echo "Starting Report BE project..."
                                cd "$report_be_dir" || exit 1
                                npm run start:dev
                                break
                                ;;
                            "stop")
                                echo "Stopping Report BE project..."
                                pid=$(lsof -t -i:5050)
                                if [ -n "$pid" ]; then
                                    sudo kill -9 "$pid"
                                else
                                    echo "No process found on port 5050"
                                fi
                                break
                                ;;
                            "openInVsCode")
                                echo "Opening Report BE project in VS Code..."
                                if [ ! -d "$report_be_dir" ]; then
                                    echo "Error: Report BE directory not found: $report_be_dir"
                                    exit 1
                                fi
                                cd "$report_be_dir" || exit 1
                                code .
                                break
                                ;;
                            "Exit")
                                echo "Exiting..."
                                exit 0
                                ;;
                            *)
                                echo "Invalid option, please choose a valid action."
                                ;;
                        esac
                    done
                    break
                    ;;
                "swithCSBwithNginx")
                    echo "You selected switch CSB with Nginx project."
                    echo "Select the action:"
                    actions=("Apache" "Nginx" "Exit")
                    select action in "${actions[@]}"
                    do
                        case $action in
                            "Apache")
                                echo "Switching to Apache..."
                                # stop entrypoint_nginx_1
                                docker stop entrypoint_nginx_1
                                sudo service apache2 start
                                break
                                ;;
                            "Nginx")
                                echo "Switching to Nginx..."
                                sudo service apache2 stop
                                docker start entrypoint_nginx_1
                                break
                                ;;
                            "Exit")
                                echo "Exiting..."
                                exit 0
                                ;;
                            *)
                                echo "Invalid option, please choose a valid action."
                                ;;
                        esac
                    done
                    break
                    ;;      
                            
                "Exit")
                    echo "Exiting..."
                    exit 0
                    ;;
                *)
                    echo "Invalid option, please choose a valid project."
                    ;;
            esac
        done
        ;;
    # "start")
    #     read -p "Do you want to start admin-csb services? (y/n): " start_admin
    #     if [ "$start_admin" = "y" ]; then
    #         sudo service apache2 start
    #     elif [ "$start_admin" = "n" ]; then
    #         echo "Stopping admin-csb services..."
    #         sudo service apache2 stop
    #     fi

    #     read -p "Do you want to start admin-frontend services? (y/n): " start_admin_frontend
    #     if [ "$start_admin_frontend" = "y" ]; then
    #         echo "Starting admin frontend services in directory: $admin"
    #         if [ ! -d "$admin" ]; then
    #             echo "Error: Admin frontend directory not found: $admin"
    #             exit 1
    #         fi
    #         cd "$admin" || exit 1
    #         npm start
    #     fi

    #     read -p "Do you want to start entrypoint services? (y/n): " start_entrypoint
    #     if [ "$start_entrypoint" = "y" ]; then
    #         echo "Moving to entrypoint services directory: $docker_entrypoint_dir"
    #         if [ ! -d "$docker_entrypoint_dir" ]; then
    #             echo "Error: Entrypoint directory not found: $docker_entrypoint_dir"
    #             exit 1
    #         fi
    #         cd "$docker_entrypoint_dir" || exit 1
    #         docker-compose --file "$docker_entrypoint_dir/docker-compose.yml" --project-name 'entrypoint' up -d
    #     fi

    #     read -p "Do you want to start queue services? (y/n): " start_quickstart
    #     if [ "$start_quickstart" = "y" ]; then
    #         cd "$queue_dir" || exit 1
    #         read -p "Do you want to run crons? (y/n): " run_crons
    #         if [ "$run_crons" = "y" ]; then
    #             npm run start:dev:cron
    #         fi
    #         read -p "Do you want to run workers? (y/n): " run_workers
    #         if [ "$run_workers" = "y" ]; then
    #             npm run start:dev:workers
    #         fi
    #         read -p "Do you want to run queue Desktop? (y/n): " run_queue_desktop
    #         if [ "$run_queue_desktop" = "y" ]; then
    #             npm run start:dev
    #         fi
    #     fi
    #     ;;
    *)
        echo "Invalid status: $status"
        exit 1
        ;;
esac