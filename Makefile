USER = $(shell whoami)
DUMP_DIR = $(shell bash -c "source /home/<USER>/Desktop/migration/config.env && echo $$DUMP_DIR")
ENTRYPOINT_DIR = $(shell bash -c "source /home/<USER>/Desktop/migration/config.env && echo $$ENTRYPOINT_DIR")
ADMIN_DIR = $(shell bash -c "source /home/<USER>/Desktop/migration/config.env && echo $$ADMIN_DIR")
QUEUE_DIR = $(shell bash -c "source /home/<USER>/Desktop/migration/config.env && echo $$QUEUE_DIR")
ADMIN_CSB_DIR = $(shell bash -c "source /home/<USER>/Desktop/migration/config.env && echo $$ADMIN_CSB_DIR")
REPORT_BE_DIR = $(shell bash -c "source /home/<USER>/Desktop/migration/config.env && echo $$REPORT_BE_DIR")


.PHONY: admin-start admin-stop migration functions user-start user-stop test elastic-start elastic-stop reindex reindex_t docker-db docker-functions entrypoint start run help
# This Makefile is used to run various migration scripts for the Ezugi platform.

# admin-start:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='start' --admin='/var/www/html/ezugi/admin-frontend/'

# admin-stop:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='stop'

migration:
	bash /home/<USER>/Desktop/migration/migrate-local.sh --migration_dir='/home/<USER>/Desktop/migration' --db_name='ezugimainplatform_staging' --project_dir='/'

functions:
	bash /home/<USER>/Desktop/migration/migrate-local.sh --migration_dir='/home/<USER>/Desktop/migration' --db_name='ezugimainplatform_staging' --project_dir='/home/<USER>/Desktop/migration' --migrate_functions_only

# user-start:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='user-start' --admin='/var/www/html/ezugi/user-frontend/'

# user-stop:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='user-stop' --admin='/var/www/html/ezugi/user-frontend/'

test: 
	bash /home/<USER>/Desktop/migration/migrate-local.sh --migration_dir='/home/<USER>/Desktop/migration' --db_name='ezugimainplatform_staging' --project_dir='/home/<USER>/Desktop/migration' --migrate_test_only
# elastic-start:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='elastic-start' --admin='/var/www/html/ezugi/user-frontend/'	

# elastic-stop:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='elastic-stop' --admin='/var/www/html/ezugi/user-frontend/'

# reindex:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='reindex' --admin='/var/www/html/ezugi/user-frontend/'
# reindex_t:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='reindex_t' --admin='/var/www/html/ezugi/user-frontend/'
docker-db:
	bash /home/<USER>/Desktop/migration/migrate-local.sh --migration_dir='/home/<USER>/Desktop/migration' --db_name='ezugimainplatform_staging' --project_dir='/home/<USER>/Desktop/migration' --docker_db_only
docker-functions:
	bash /home/<USER>/Desktop/migration/migrate-local.sh --migration_dir='/home/<USER>/Desktop/migration' --db_name='ezugimainplatform_staging' --project_dir='/home/<USER>/Desktop/migration' --docker_functions_only
# entrypoint:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='entrypoint' --admin='$(ADMIN_DIR)' --docker-entrypoint-dir='$(ENTRYPOINT_DIR)'
# start:
# 	bash /home/<USER>/Desktop/migration/start-admin.sh --status='start' --admin='$(ADMIN_DIR)' --docker-entrypoint-dir='$(ENTRYPOINT_DIR)' --queue-dir='$(QUEUE_DIR)'
run:
	bash /home/<USER>/Desktop/migration/start-admin.sh --status='run' --admin='$(ADMIN_DIR)' --docker-entrypoint-dir='$(ENTRYPOINT_DIR)' --queue-dir='$(QUEUE_DIR)' --report-be-dir='$(REPORT_BE_DIR)' 
help:
	@echo "Available commands:"
	@echo "  migration          - Run the migration script."
	@echo "  functions          - Run the migration script for functions only."
	@echo "  test               - Run the migration script for testing only."
	@echo "  docker-db          - Run the migration script for Docker database only."
	@echo "  docker-functions   - Run the migration script for Docker functions only."
	@echo "  entrypoint         - PULL latest User FE and User BE images and run make setup , Make dirty-up "
	@echo "  run                - A to Z migration script for the Ezugi platform."


