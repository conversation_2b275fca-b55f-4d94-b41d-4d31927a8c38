#!/bin/bash

# Fixed Augment VIP Installer
# This script uses the working virtualenv tool instead of the problematic built-in venv

set -e

echo "[INFO] Starting Augment VIP installation with fixed virtual environment..."

# Check if Python 3.12 is available
if ! command -v python3.12 &> /dev/null; then
    echo "[ERROR] Python 3.12 is required but not found"
    exit 1
fi

# Check if virtualenv is available
if ! command -v /home/<USER>/.local/bin/virtualenv &> /dev/null; then
    echo "[ERROR] virtualenv tool not found. Please install it first."
    exit 1
fi

# Download the installation script and files
echo "[INFO] Downloading Augment VIP files..."
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/install.sh -o install.sh
chmod +x install.sh

# Create project directory
PROJECT_DIR="augment-vip-fixed"
echo "[INFO] Creating project directory: $PROJECT_DIR"
mkdir -p "$PROJECT_DIR"
cd "$PROJECT_DIR"

# Download all required files
echo "[INFO] Downloading Python installer..."
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/install.py -o install.py

echo "[INFO] Downloading package files..."
mkdir -p augment_vip
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/augment_vip/__init__.py -o augment_vip/__init__.py
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/augment_vip/utils.py -o augment_vip/utils.py
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/augment_vip/db_cleaner.py -o augment_vip/db_cleaner.py
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/augment_vip/id_modifier.py -o augment_vip/id_modifier.py
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/augment_vip/cli.py -o augment_vip/cli.py
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/setup.py -o setup.py
curl -fsSL https://raw.githubusercontent.com/azrilaiman2003/augment-vip/main/requirements.txt -o requirements.txt

# Create virtual environment using our working virtualenv
echo "[INFO] Creating virtual environment with working virtualenv..."
/home/<USER>/.local/bin/virtualenv -p python3.12 .venv

# Activate virtual environment and install
echo "[INFO] Installing requirements..."
source .venv/bin/activate
pip install -r requirements.txt

echo "[INFO] Installing Augment VIP package..."
pip install -e .

# Create convenience script
cat > run-augment-vip.sh << 'SCRIPT_EOF'
#!/bin/bash
cd "$(dirname "$0")"
source .venv/bin/activate
augment-vip "$@"
SCRIPT_EOF
chmod +x run-augment-vip.sh

echo "[SUCCESS] Augment VIP installed successfully!"
echo "[INFO] Location: $(pwd)"
echo "[INFO] Usage:"
echo "  ./run-augment-vip.sh --help"
echo "  ./run-augment-vip.sh clean"
echo "  ./run-augment-vip.sh modify-ids"
echo "  ./run-augment-vip.sh all"

