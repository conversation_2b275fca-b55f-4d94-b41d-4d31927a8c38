# 📚 JavaScript Data Types: Primitives vs References

**Date:** 2025-07-04  
**Status:** 🎯 In Progress  
**Estimated Time:** 25 minutes  
**Prerequisites:** ✅ Variables (let, const, var)

---

## 🎯 Learning Objectives
By the end of this lesson, you will:
1. Understand all JavaScript primitive data types
2. Know the difference between primitive and reference types
3. Learn type checking with `typeof`
4. Understand type coercion and conversion
5. Write type-safe JavaScript code

---

## 📖 Theory & Explanation

### 🔍 What are Data Types?

Data types define what kind of information a variable can store. JavaScript has **two main categories**:

1. **Primitive Types** - Simple, immutable values
2. **Reference Types** - Complex, mutable objects

---

## 🎯 Primitive Data Types (7 types)

### 1. **String** - Text Data
```javascript
const firstName = "Alice";
const lastName = 'Smith';
const message = `Hello, ${firstName}!`; // Template literal
const multiLine = `Line 1
Line 2
Line 3`;

console.log(typeof firstName); // "string"
```

**Key Points:**
- Use single quotes `'`, double quotes `"`, or backticks `` ` ``
- Backticks allow template literals and multi-line strings
- Strings are immutable (cannot be changed)

### 2. **Number** - Numeric Data
```javascript
const age = 25;           // Integer
const price = 19.99;      // Decimal
const negative = -10;     // Negative
const infinity = Infinity; // Special number
const notANumber = NaN;   // "Not a Number"

console.log(typeof age); // "number"
```

**Special Numbers:**
- `Infinity` and `-Infinity`
- `NaN` (Not a Number) - result of invalid math operations

### 3. **Boolean** - True/False
```javascript
const isLoggedIn = true;
const isComplete = false;
const hasPermission = Boolean(1); // true

console.log(typeof isLoggedIn); // "boolean"
```

### 4. **Undefined** - Declared but Not Assigned
```javascript
let userName; // Declared but not assigned
console.log(userName); // undefined
console.log(typeof userName); // "undefined"

// Function with no return value
function doSomething() {
    // No return statement
}
console.log(doSomething()); // undefined
```

### 5. **Null** - Intentionally Empty
```javascript
let data = null; // Intentionally set to "nothing"
console.log(data); // null
console.log(typeof data); // "object" (this is a JavaScript bug!)
```

**Important:** `typeof null` returns `"object"` - this is a known JavaScript quirk!

### 6. **Symbol** - Unique Identifier (ES6+)
```javascript
const id1 = Symbol('id');
const id2 = Symbol('id');
console.log(id1 === id2); // false - each symbol is unique

console.log(typeof id1); // "symbol"
```

### 7. **BigInt** - Large Integers (ES2020+)
```javascript
const bigNumber = 123456789012345678901234567890n;
const anotherBig = BigInt("123456789012345678901234567890");

console.log(typeof bigNumber); // "bigint"
```

---

## 🏗️ Reference Types (Objects)

### **Object** - Key-Value Pairs
```javascript
const person = {
    name: "John",
    age: 30,
    isStudent: false
};

console.log(typeof person); // "object"
```

### **Array** - Ordered List
```javascript
const colors = ["red", "green", "blue"];
const numbers = [1, 2, 3, 4, 5];
const mixed = [1, "hello", true, null];

console.log(typeof colors); // "object" (arrays are objects!)
console.log(Array.isArray(colors)); // true - better way to check
```

### **Function** - Reusable Code Block
```javascript
function greet(name) {
    return `Hello, ${name}!`;
}

const sayHi = function() {
    return "Hi there!";
};

console.log(typeof greet); // "function"
```

---

## 🔍 Type Checking & Conversion

### **Checking Types with `typeof`**
```javascript
console.log(typeof "hello");     // "string"
console.log(typeof 42);          // "number"
console.log(typeof true);        // "boolean"
console.log(typeof undefined);   // "undefined"
console.log(typeof null);        // "object" (quirk!)
console.log(typeof {});          // "object"
console.log(typeof []);          // "object"
console.log(typeof function(){}); // "function"
```

### **Better Type Checking**
```javascript
// More accurate type checking
function getType(value) {
    if (value === null) return "null";
    if (Array.isArray(value)) return "array";
    return typeof value;
}

console.log(getType(null));     // "null"
console.log(getType([]));       // "array"
console.log(getType({}));       // "object"
```

### **Type Conversion**
```javascript
// String conversion
const num = 42;
const str1 = String(num);        // "42"
const str2 = num.toString();     // "42"
const str3 = `${num}`;           // "42"

// Number conversion
const text = "123";
const num1 = Number(text);       // 123
const num2 = parseInt(text);     // 123
const num3 = parseFloat("123.45"); // 123.45

// Boolean conversion
const bool1 = Boolean(1);        // true
const bool2 = Boolean(0);        // false
const bool3 = Boolean("");       // false
const bool4 = Boolean("hello");  // true
```

---

## 🎯 Primitive vs Reference Behavior

### **Primitives are Copied by Value**
```javascript
let a = 5;
let b = a;  // b gets a COPY of a's value
a = 10;     // Changing a doesn't affect b

console.log(a); // 10
console.log(b); // 5 (unchanged)
```

### **Objects are Copied by Reference**
```javascript
let obj1 = { name: "Alice" };
let obj2 = obj1;  // obj2 points to the SAME object
obj1.name = "Bob"; // Changing obj1 affects obj2

console.log(obj1.name); // "Bob"
console.log(obj2.name); // "Bob" (same object!)
```

---

## 💡 Practical Examples

### **Example 1: User Profile**
```javascript
// Using different data types appropriately
const userProfile = {
    id: Symbol('user'),           // Unique identifier
    username: "john_doe",         // String
    age: 28,                      // Number
    isActive: true,               // Boolean
    lastLogin: null,              // Null (no last login yet)
    preferences: undefined,       // Undefined (not set)
    followers: ["alice", "bob"],  // Array
    settings: {                   // Object
        theme: "dark",
        notifications: true
    }
};
```

### **Example 2: Type Validation**
```javascript
function validateInput(value, expectedType) {
    const actualType = typeof value;
    
    if (expectedType === "array" && Array.isArray(value)) {
        return true;
    }
    
    if (expectedType === "null" && value === null) {
        return true;
    }
    
    return actualType === expectedType;
}

// Usage
console.log(validateInput("hello", "string")); // true
console.log(validateInput(42, "string"));      // false
console.log(validateInput([], "array"));       // true
```

---

## 🧪 Hands-On Practice

### **Practice Exercise 1: Type Detective**
```javascript
// What type is each variable? Use typeof or better methods
const mystery1 = "123";
const mystery2 = 123;
const mystery3 = true;
const mystery4 = null;
const mystery5 = undefined;
const mystery6 = [];
const mystery7 = {};
const mystery8 = function() {};

// Your answers:
// mystery1: ?
// mystery2: ?
// mystery3: ?
// etc...
```

### **Practice Exercise 2: Fix the Bug**
```javascript
// This code has type-related bugs. Can you fix them?
function calculateTotal(price, quantity) {
    return price * quantity;
}

const result1 = calculateTotal("10", "5");  // What's wrong here?
const result2 = calculateTotal(10, null);   // And here?

console.log(result1); // What will this output?
console.log(result2); // What will this output?
```

---

## ❓ Knowledge Check Questions

1. **What's the difference between `null` and `undefined`?**
2. **Why does `typeof null` return "object"?**
3. **How can you check if a variable is an array?**
4. **What happens when you add a string and a number?**
5. **What's the difference between primitive and reference types?**

---

## 🎯 Quick Quiz

**Question 1:** What will this code output?
```javascript
let x = "5";
let y = 3;
console.log(x + y);
```
- A) 8
- B) "53"
- C) Error

**Question 2:** What's the best way to check if a variable is an array?
- A) `typeof arr === "array"`
- B) `Array.isArray(arr)`
- C) `arr instanceof Object`

---

## 📝 Summary & Key Takeaways

### 🔑 Key Points:
1. **7 Primitive Types**: string, number, boolean, undefined, null, symbol, bigint
2. **Reference Types**: objects, arrays, functions
3. **typeof operator** - useful but has quirks (null, arrays)
4. **Primitives** are copied by value, **objects** by reference
5. **Type conversion** can be explicit or implicit (coercion)

### 🎯 Next Steps:
- Practice type checking in browser console
- Experiment with type conversion
- Ready for JavaScript Scope concepts!

---

## 📚 Additional Resources
- [MDN: JavaScript Data Types](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Data_structures)
- [MDN: typeof operator](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Operators/typeof)
