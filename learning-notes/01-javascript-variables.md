# 📚 JavaScript Variables: let vs const vs var

**Date:** 2025-07-04  
**Status:** 🎯 In Progress  
**Estimated Time:** 20 minutes  

---

## 🎯 Learning Objectives
By the end of this lesson, you will:
1. Understand the differences between `let`, `const`, and `var`
2. Know when to use each variable declaration
3. Understand scope and hoisting behavior
4. Write modern JavaScript with best practices

---

## 📖 Theory & Explanation

### 🔍 What are Variable Declarations?

Variable declarations are how we create and name storage containers for data in JavaScript. Think of them as labeled boxes where you store information.

### 🆚 The Three Types: var, let, const

#### 1. `var` - The Old Way (Avoid in Modern JS)
```javascript
var name = "<PERSON>";
var age = 25;
var age = 30; // Can redeclare - this is problematic!
```

**Characteristics:**
- ❌ Function-scoped (not block-scoped)
- ❌ Can be redeclared
- ❌ Hoisted and initialized with `undefined`
- ❌ Creates global properties

#### 2. `let` - For Variables That Change
```javascript
let score = 0;
score = 10; // ✅ Can reassign
score = 20; // ✅ Can reassign again

// let score = 30; // ❌ Cannot redeclare in same scope
```

**Characteristics:**
- ✅ Block-scoped
- ✅ Cannot be redeclared in same scope
- ✅ Can be reassigned
- ✅ Hoisted but not initialized (Temporal Dead Zone)

#### 3. `const` - For Constants
```javascript
const PI = 3.14159;
const userName = "Alice";

// PI = 3.14; // ❌ Cannot reassign
// const age; // ❌ Must initialize when declaring
```

**Characteristics:**
- ✅ Block-scoped
- ✅ Cannot be redeclared
- ❌ Cannot be reassigned
- ✅ Must be initialized at declaration
- ✅ Hoisted but not initialized (Temporal Dead Zone)

---

## 🔬 Scope Demonstration

### Block Scope Example
```javascript
function demonstrateScope() {
    if (true) {
        var varVariable = "I'm var";
        let letVariable = "I'm let";
        const constVariable = "I'm const";
    }
    
    console.log(varVariable);    // ✅ "I'm var" - accessible
    console.log(letVariable);    // ❌ ReferenceError
    console.log(constVariable);  // ❌ ReferenceError
}
```

### Hoisting Behavior
```javascript
// What happens with hoisting?
console.log(varExample);    // undefined (hoisted, initialized)
console.log(letExample);    // ReferenceError (hoisted, not initialized)
console.log(constExample);  // ReferenceError (hoisted, not initialized)

var varExample = "var value";
let letExample = "let value";
const constExample = "const value";
```

---

## 🎯 Best Practices & When to Use Each

### ✅ Modern JavaScript Rules:
1. **Use `const` by default** - for values that won't change
2. **Use `let`** - when you need to reassign the variable
3. **Avoid `var`** - it's legacy and causes problems

### 📋 Decision Tree:
```
Will this value change? 
├── No → Use `const`
└── Yes → Use `let`

Never use `var` in modern JavaScript!
```

---

## 💡 Practical Examples

### Example 1: Counter Application
```javascript
// ✅ Good: Using let for changing values
let count = 0;
const increment = () => {
    count = count + 1; // Reassignment needed
};

// ✅ Good: Using const for unchanging values
const maxCount = 100;
const appName = "Counter App";
```

### Example 2: Loop Variables
```javascript
// ❌ Bad: var in loops
for (var i = 0; i < 3; i++) {
    setTimeout(() => console.log(i), 100); // Prints: 3, 3, 3
}

// ✅ Good: let in loops
for (let i = 0; i < 3; i++) {
    setTimeout(() => console.log(i), 100); // Prints: 0, 1, 2
}
```

---

## 🧪 Hands-On Practice

### Practice Exercise 1: Fix the Code
```javascript
// Fix this code using modern variable declarations:
var userName = "student";
var isLoggedIn = false;
var maxAttempts = 3;

function login() {
    var attempts = 0;
    if (attempts < maxAttempts) {
        isLoggedIn = true;
        attempts = attempts + 1;
    }
}
```

### Practice Exercise 2: Scope Challenge
```javascript
// What will this code output? Why?
function scopeTest() {
    if (true) {
        var a = 1;
        let b = 2;
        const c = 3;
    }
    console.log(a); // ?
    console.log(b); // ?
    console.log(c); // ?
}
```

---

## ❓ Knowledge Check Questions

1. **What's the main difference between `let` and `const`?**
2. **Why should you avoid `var` in modern JavaScript?**
3. **What happens if you try to use a `let` variable before declaring it?**
4. **Can you change the contents of a `const` object or array?**
5. **What is the "Temporal Dead Zone"?**

---

## 🎯 Quick Quiz

**Question 1:** Which declaration should you use for a user's age that might change?
- A) `var age = 25`
- B) `let age = 25`
- C) `const age = 25`

**Question 2:** What will this code output?
```javascript
const numbers = [1, 2, 3];
numbers.push(4);
console.log(numbers);
```
- A) Error - cannot modify const
- B) [1, 2, 3, 4]
- C) [1, 2, 3]

---

## 📝 Summary & Key Takeaways

### 🔑 Key Points:
1. **`const`** - Use by default for values that don't change
2. **`let`** - Use when you need to reassign the variable
3. **`var`** - Avoid completely in modern JavaScript
4. **Block scope** - `let` and `const` are block-scoped, `var` is function-scoped
5. **Hoisting** - All are hoisted, but `let`/`const` have Temporal Dead Zone

### 🎯 Next Steps:
- Practice with the exercises above
- Try creating variables in your browser console
- Move on to JavaScript Data Types once comfortable

---

## 📚 Additional Resources
- [MDN: let](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/let)
- [MDN: const](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/const)
- [MDN: var](https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Statements/var)
