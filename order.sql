DO $$
DECLARE
    seq_record RECORD;
    table_name TEXT;
    column_name TEXT := 'id';  -- Assuming the ID column is named 'id'
    max_id BIGINT;
BEGIN
    -- Loop over all sequences in the current schema
    FOR seq_record IN
        SELECT sequence_name, sequence_schema
        FROM information_schema.sequences
        WHERE sequence_schema = 'public' -- Adjust if necessary
    LOOP
        -- Derive the table name from the sequence name
        table_name := substring(seq_record.sequence_name FROM 1 FOR length(seq_record.sequence_name) - 7);  -- Remove '_id_seq'

        BEGIN
            -- Try to get the max ID from the dynamically constructed table name
            EXECUTE format('SELECT MAX(%I) FROM %I.%I', column_name, seq_record.sequence_schema, table_name) INTO max_id;

            -- If the max ID is not null, set the sequence value to max_id + 1
            IF max_id IS NOT NULL THEN
                EXECUTE format('SELECT setval(%L, %s)', seq_record.sequence_name, max_id + 1);
                RAISE NOTICE 'Table "%" updated, setting sequence to %', table_name, max_id + 1;
            ELSE
                RAISE NOTICE 'Table "%" has no entries, skipping sequence update.', table_name;
            END IF;

        EXCEPTION
            WHEN undefined_table THEN
                RAISE NOTICE 'Table "%" does not exist, skipping.', table_name;
            WHEN OTHERS THEN
                RAISE NOTICE 'An error occurred while processing table "%": %', table_name, SQLERRM;
        END;
    END LOOP;
END $$;



CREATE SEQUENCE IF NOT EXISTS user_bonus_queue_order_seq START 1 INCREMENT 1 MINVALUE 1;
ALTER TABLE public.user_bonus_queue ALTER COLUMN ordering SET DEFAULT nextval('user_bonus_queue_order_seq'::regclass);
SELECT setval('user_bonus_queue_order_seq', (SELECT MAX(ordering) FROM user_bonus_queue));

CREATE UNIQUE INDEX IF NOT EXISTS user_tenant_provider_type_transaction_uidx ON player_summary_provider_wise (user_id, tenant_id, type, date, COALESCE(provider_id, 0), COALESCE(game_id, ''), currency_id, agent_id);