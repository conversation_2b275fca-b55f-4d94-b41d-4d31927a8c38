#!/bin/bash
# ezugimainplatform_prod.sql
migrate_functions_only=false
migrate_test_only=false  # Initialize this variable
docker_functions_only=false

for arg in "$@"
do
    case $arg in
        --migration_dir=*)
            migration_dir="${arg#*=}"
            shift
        ;;
        --db_name=*)
            db_name="${arg#*=}"
            shift
        ;;
        --project_dir=*)
            project_dir="${arg#*=}"
            shift
        ;;
        --migrate_functions_only)
            migrate_functions_only=true
            shift
        ;;
        --migrate_test_only)
            migrate_test_only=true
            shift
        ;;
        --docker_db_only)
            docker_db_only=true
            shift
        ;;
        --docker_functions_only)
            docker_functions_only=true
            shift
        ;;
        --dump_dir=*)
            shift
        ;;
        *)
            echo "Unknown argument: $arg"
            exit 1
        ;;
    esac
done

if [[ -z "$migration_dir" || -z "$db_name" || -z "$project_dir" ]]; then
    echo "Error: Missing required arguments."
    echo "Usage: $0 --migration_dir='...' --db_name='...' --project_dir='...' [--migrate_functions_only]"
    exit 1
fi

echo "Migration Directory: $migration_dir"
echo "Database Name: $db_name"
echo "Project Directory: $project_dir"
echo "Migrate Functions Only: $migrate_functions_only"

# Prompt for PostgreSQL password
# Prompt for PostgreSQL password
# ask psdtgres user 

read -s -p "Enter PostgreSQL password: " PGPASSWORD
export PGPASSWORD
echo



# Only ask for a custom dump if both migrate_functions_only and migrate_test_only are false
if [[ "$migrate_functions_only" = false && "$migrate_test_only" = false && "$docker_functions_only" = false ]]; then
    # Ask Custom Dump Yes or No
    read -p "Do you want to use a custom dump? (y/n): " use_custom_dump
    if [[ "$use_custom_dump" == "y" || "$use_custom_dump" == "Y" || "$use_custom_dump" == "yes" || "$use_custom_dump" == "YES" ]]; then
        read -p "Enter the path to the custom dump file present in Downloads: " custom_dump_file
        # Full path to the custom dump file
        custom_dump="$HOME/Downloads/$custom_dump_file"
        # Check if the custom dump file exists
        if [ ! -f "$custom_dump" ]; then
            echo "Error: Custom dump file not found at $custom_dump."
            exit 1
        fi
        # Check if the custom dump file is a .sql or .gz file
        if [[ "$custom_dump" != *.sql && "$custom_dump" != *.gz ]]; then
            echo "Error: Custom dump file must be a .sql or .gz file."
            exit 1
        fi

        if [[ "$custom_dump" != *.sql ]]; then
          if [[ "$custom_dump" == *.gz ]]; then
                echo "Unzipping custom dump file..."
                gunzip -c "$custom_dump" > "$migration_dir/ezugimainplatform_staging.sql" || {
                    echo "Failed to unzip custom dump file."
                    exit 1
                }
                custom_dump="$migration_dir/ezugimainplatform_staging.sql"

                # Remove the constraint
                sed -i '/CONSTRAINT "user_tenant_provider_type_transaction_uidx" UNIQUE ("user_id", "tenant_id", "type", "date", "", "", "currency_id", "agent_id")/d' "$custom_dump"

                # Remove the trailing comma before closing parenthesis
                sed -i ':a;N;$!ba;s/,\n)/\n)/g' "$custom_dump"

                echo "Custom dump file processed successfully."
            else
                echo "Error: Custom dump file must be a .sql or .gz file."
                exit 1
            fi

        fi
        echo "Custom dump file processed successfully."
        # exit 0
    fi
fi

# Navigate to the migration directory
cd "$migration_dir" || { 
    echo "Migration directory not found: $migration_dir"; 
    exit 1; 
}

if [ "$migrate_test_only" = true ]; then
    psql -U postgres -d "$db_name" -f "$migration_dir/query.sql" || { 
        echo "Failed to execute query.sql"; 
        exit 1; 
    }
    # Unset the PostgreSQL password
    unset PGPASSWORD
    exit 0
fi

# If migrate_functions_only is true, only execute the functions SQL script
if [ "$migrate_functions_only" = true ]; then
    psql -U postgres -d "$db_name" -f "$migration_dir/functions.sql" || { 
        echo "Failed to execute functions.sql"; 
        exit 1; 
    }
    # Unset the PostgreSQL password
    unset PGPASSWORD
    exit 0
fi


if [ "$docker_db_only" = true ]; then
    # first copy the ezugimainplatform_staging.sql file to the docker container 
    # docker cp /home/<USER>/Downloads/ezugi-main-platform-staging-12.sql.sql entrypoint_database_1:/file1.sql
    docker cp "$migration_dir/ezugimainplatform_staging.sql" entrypoint_database_1:/file1.sql
    docker cp "$migration_dir/functions.sql" entrypoint_database_1:/functions.sql
    docker cp "$migration_dir/order.sql" entrypoint_database_1:/order.sql
    docker cp "$migration_dir/views.sql" entrypoint_database_1:/views.sql
    # then run the following command to execute the SQL file inside the container
    # drop the database if it exists
    docker exec -i entrypoint_database_1 psql -U postgres -c "DROP DATABASE IF EXISTS $db_name;" || { 
        echo "Failed to drop database $db_name"; 
        exit 1; 
    }
    # create the database
    docker exec -i entrypoint_database_1 psql -U postgres -c "CREATE DATABASE $db_name;" || { 
        echo "Failed to create database $db_name"; 
        exit 1; 
    }
# CREATE USER postgres WITH ENCRYPTED PASSWORD 'postgres';
    # create the user
    # docker exec -i entrypoint_database_1 psql -U postgres -c "CREATE USER postgres WITH ENCRYPTED PASSWORD 'postgres';" || { 
    #     echo "Failed to create user postgres"; 
    #     exit 1; 
    # }
    

    # grant privileges to the database
    docker exec -i entrypoint_database_1 psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE $db_name TO postgres;" || { 
        echo "Failed to grant privileges on database $db_name"; 
        exit 1; 
    }
    # create the hstore extension
    docker exec -i entrypoint_database_1 psql -U postgres -d "$db_name" -c "CREATE EXTENSION hstore;" || { 
        echo "Failed to create hstore extension"; 
        exit 1; 
    }
    docker exec -i entrypoint_database_1 psql -U postgres -d "$db_name" -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";" || { 
        echo "Failed to create uuid-ossp extension"; 
        exit 1; 
    }

    docker exec -i entrypoint_database_1 psql -U postgres -d "$db_name" -f /file1.sql || { 
        echo "Failed to execute ezugimainplatform_staging.sql"; 
        exit 1; 
    }
    docker exec -i entrypoint_database_1 psql -U postgres -d "$db_name" -f /functions.sql || { 
        echo "Failed to execute functions.sql"; 
        exit 1; 
    }
    docker exec -i entrypoint_database_1 psql -U postgres -d "$db_name" -f /order.sql || { 
        echo "Failed to execute order.sql"; 
        exit 1; 
    }
    # docker exec -i entrypoint_database_1 psql -U postgres -d "$db_name" -f /views.sql || { 
    #     echo "Failed to execute views.sql"; 
    #     exit 1; 
    # }
    # Unset the PostgreSQL password
    unset PGPASSWORD
    exit 0


fi

# docker functions only
if [ "$docker_functions_only" = true ]; then
    # first copy the ezugimainplatform_staging.sql file to the docker container 
    # docker cp /home/<USER>/Downloads/ezugi-main-platform-staging-12.sql.sql entrypoint_database_1:/file1.sql
    docker cp "$migration_dir/functions.sql" entrypoint_database_1:/functions.sql
    # then run the following command to execute the SQL file inside the container
    # drop the database if it exists
    # create the database
    # CREATE USER postgres WITH ENCRYPTED PASSWORD 'postgres';
    # grant privileges to the database
    # create the hstore extension
    docker exec -i entrypoint_database_1 psql -U postgres -d "$db_name" -f /functions.sql || { 
        echo "Failed to execute functions.sql"; 
        exit 1; 
    }
    # Unset the PostgreSQL password
    unset PGPASSWORD
    exit 0
fi

# Navigate to the project directory
cd "$project_dir" || { 
    echo "Project directory not found: $project_dir"; 
    exit 1; 
}

# Stop Apache server
sudo service apache2 stop || { 
    echo "Failed to stop Apache server"; 
    exit 1; 
}

# Recreate the database
psql -U postgres -c "DROP DATABASE IF EXISTS $db_name;" || { 
    echo "Failed to drop database $db_name"; 
    exit 1; 
}
psql -U postgres -c "CREATE DATABASE $db_name;" || { 
    echo "Failed to create database $db_name"; 
    exit 1; 
}






psql -U postgres -c "GRANT ALL PRIVILEGES ON DATABASE $db_name TO postgres;" || { 
    echo "Failed to grant privileges on database $db_name"; 
    exit 1; 
}

# Create the hstore extension
psql -U postgres -d "$db_name" -c "CREATE EXTENSION hstore;" || { 
    echo "Failed to create hstore extension"; 
    exit 1; 
}
# CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
psql -U postgres -d "$db_name" -c "CREATE EXTENSION IF NOT EXISTS \"uuid-ossp\";" || { 
    echo "Failed to create uuid-ossp extension"; 
    exit 1; 
}


# Execute the functions SQL script
 psql -U postgres -d "$db_name" -f "$migration_dir/functions.sql" || { 
        echo "Failed to execute functions.sql"; 
        exit 1; 
}

# # sequence.sql
# psql -U postgres -d "$db_name" -f "$migration_dir/sequence.sql" || { 
#     echo "Failed to execute sequence.sql"; 
#     exit 1; 
# }

# Execute the migration SQL script
psql -U postgres -d "$db_name" -f "$migration_dir/ezugimainplatform_staging.sql" || { 
    echo "Failed to execute ezugimainplatform_staging.sql"; 
    exit 1; 
}
# Execute the functions SQL script
#  psql -U postgres -d "$db_name" -f "$migration_dir/functions.sql" || { 
#         echo "Failed to execute functions.sql"; 
#         exit 1; 
# }
# Execute the functions SQL script
psql -U postgres -d "$db_name" -f "$migration_dir/order.sql" || { 
    echo "Failed to execute order.sql"; 
    exit 1; 
}

# Execute the functions SQL script
# psql -U postgres -d "$db_name" -f "$migration_dir/views.sql" || { 
#     echo "Failed to execute order.sql"; 
#     exit 1; 
# }
# Start Apache server
sudo service apache2 start || { 
    echo "Failed to start Apache server";
    exit 1; 
}

# remove the ezugimainplatform_staging.sql file in the migration directory
rm -f "$migration_dir/ezugimainplatform_staging.sql" || { 
    echo "Failed to remove ezugimainplatform_staging.sql"; 
    exit 1; 
}
# Unset the PostgreSQL password
unset PGPASSWORD

