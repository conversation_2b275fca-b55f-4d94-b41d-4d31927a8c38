bash /home/<USER>/Desktop/migration/migrate-local.sh --migration_dir='/home/<USER>/Desktop/migration' --db_name='ezugimainplatform_staging' --project_dir='/'


DATABASE NAME
ezugimainplatform_staging.sql

<!-- table chnage uiique  -->

remove user_tenant_provider_type_transaction_uidx from migration file
",
    CONSTRAINT "user_tenant_provider_type_transaction_uidx" UNIQUE ("user_id", "tenant_id", "type", "date", "", "", "currency_id", "agent_id")"

<!-- GET ALL Procedure and functions  -->

SELECT string_agg(pg_catalog.pg_get_functiondef(p.oid), '; ') AS all_function_definitions
FROM pg_catalog.pg_proc p
LEFT JOIN pg_catalog.pg_namespace n ON n.oid = p.pronamespace
WHERE n.nspname = 'public'  -- or change to your schema
  AND pg_catalog.pg_function_is_visible(p.oid);

  <!-- sequence  -->
SELECT string_agg(
  'CREATE SEQUENCE ' || quote_ident(sequence_schema) || '.' || quote_ident(sequence_name) ||
  ' INCREMENT BY ' || increment ||
  ' MINVALUE ' || minimum_value ||
  ' MAXVALUE ' || maximum_value ||
  ' START WITH ' || start_value ||
  ' ' || CASE cycle_option WHEN 'YES' THEN 'CYCLE' ELSE 'NO CYCLE' END,
  ';'
) || ';' AS all_sequence_creations
FROM information_schema.sequences
WHERE sequence_schema = 'public';  -- Change schema if needed


<!-- all views -->

SELECT string_agg('CREATE OR REPLACE VIEW ' || schemaname || '.' || viewname || ' AS ' || definition, '; ') AS all_view_definitions
FROM pg_views
WHERE schemaname = 'public';
  <!--  -->
select menu_tenant_setting.id, menu_master.name,pages.id, pages.title, page_menus.id, casino_menus.name, menu_items.id, menu_items.name, casino_items.uuid from menu_tenant_setting

left join menu_master on menu_master.id = menu_tenant_setting.menu_id
left join pages on pages.top_menu_id =   menu_tenant_setting.id
left join page_menus on pages.id = page_menus.page_id
left join casino_menus on  page_menus.casino_menu_id = casino_menus.id
left join menu_items on page_menus.id = menu_items.page_menu_id
left join casino_items    on menu_items.casino_item_id = casino_items.id

where
        menu_tenant_setting.tenant_id = 1  and menu_tenant_setting.status = true and                                -- Casino Pages
menu_master.active = true and                                                                               -- Casino Pages (Names)
        pages.tenant_id = menu_tenant_setting.tenant_id and pages.enabled = true  and                               -- Casino Page Items
-- page_menus                                                                                               -- Casino Page menus
        casino_menus.tenant_id = menu_tenant_setting.tenant_id and  casino_menus.enabled= true and                  -- Casino Page menus(Casino menu name)
menu_items.active = true  and                                                                               -- Casino Page Menu Items
        casino_items.tenant_id = menu_tenant_setting.tenant_id  and casino_items.active = true                      -- Casino Page Menu Items (uuid name)

-- and menu_tenant_setting.id = 100 and page_id = 9 and page_menus.id = 32
 

<!-- RENAME -->
 sql.csv Rename to functions.sql

<!-- elastic REINDEX -->
http://localhost/ezugi/admin-csb/backend/reindex-user
http://localhost/ezugi/admin-csb/backend/reindex-transaction
http://localhost/ezugi/admin-csb/backend/reindex-bet-transaction
http://localhost/ezugi/admin-csb/backend/reindex-audit-log


<!-- log php-->

add top DB::enableQueryLog(); // Start query logging
before return add dd(DB::getQueryLog())

*************:5432
<!-- IP -->
sudo ufw disable
sudo ufw allow 5432/tcp
***************
sudo nano /etc/postgresql/14/main/pg_hba.conf
host    all             all             *************/24          md5

sudo nano /etc/postgresql/14/main/postgresql.conf
listen_addresses = '*'

sudo ufw allow from *************/24 to any port 5432

sudo systemctl restart postgresql

verify
psql -h ************* -U postgres -d <database_name>




if any error like 
original: error: no pg_hba.conf entry for host "**********", user "postgres", database "ezugi-main", no encryption
      at Connection.parseE (/home/<USER>/app/node_modules/pg/lib/connection.js:606:11)
      at Connection.parseMessage (/home/<USER>/app/node_modules/pg/lib/connection.js:403:19)
      at Socket.<anonymous> (/home/<USER>/app/node_modules/pg/lib/connection.js:123:22)
      at Socket.emit (events.js:210:5)
      at Socket.EventEmitter.emit (domain.js:475:20)
      at addChunk (_stream_readable.js:309:12)
      at readableAddChunk (_stream_readable.js:290:11)
      at Socket.Readable.push (_stream_readable.js:224:10)


sudo nano /etc/postgresql/14/main/pg_hba.conf

host    all             all             **********/16           md5


```
select
--     menu_tenant_setting.id,
    menu_master.name, casino_items.provider,  casino_providers.name, pages.id, pages.title, pages.enabled, page_menus.id, casino_menus.name, casino_menus.enabled , menu_items.id, menu_items.name, menu_items.active , casino_items.uuid , casino_items.active

-- from menu_tenant_setting
from menu_master

-- left join menu_master on menu_master.id = menu_tenant_setting.menu_id
left join pages on pages.top_menu_id = menu_master.id
left join page_menus on pages.id = page_menus.page_id
left join casino_menus on  page_menus.casino_menu_id = casino_menus.id
left join menu_items on page_menus.id = menu_items.page_menu_id
left join casino_items    on menu_items.casino_item_id = casino_items.id
left join casino_providers on casino_items.provider::numeric = casino_providers.id

where
menu_master.active = true and                                                                               -- Casino Pages (Names)
   pages.enabled = true  and   pages.tenant_id = 0 and                       -- Casino Page Items                                                                                           -- Casino Page menus
        casino_menus.tenant_id = pages.tenant_id and  casino_menus.enabled= true and                  -- Casino Page menus(Casino menu name)
menu_items.active = true  and                                                                               -- Casino Page Menu Items
        casino_items.tenant_id = pages.tenant_id   and casino_items.active = true                      -- Casino Page Menu Items (uuid name)
and casino_providers.id = 5099


cd augment-vip && source .venv/bin/activate && augment-vip all

<!-- MAKEFILE COMMANDS -->

## Makefile Usage

The project includes a Makefile for easy execution of migration and setup commands:

### Available Commands:

```bash
# Show all available commands
make help

# Run the main migration script
make migration

# Run migration for functions only
make functions

# Run migration for testing only
make test

# Run Docker database migration only
make docker-db

# Run Docker functions migration only
make docker-functions

# Run complete A to Z migration for Ezugi platform
make run

# Clean up containers, volumes, and temporary files
make clean

# Clean VS Code databases and modify telemetry IDs using Augment VIP
make augment-clean
```

### Command Details:

- **`make migration`**: Runs the main migration script with database 'ezugimainplatform_staging'
- **`make functions`**: Migrates functions only to the staging database
- **`make test`**: Runs migration in test mode only
- **`make docker-db`**: Handles Docker database migration specifically
- **`make docker-functions`**: Handles Docker functions migration specifically
- **`make run`**: Complete migration script including admin, entrypoint, queue, and report backend directories
- **`make clean`**: Clean up containers, volumes, and temporary files
- **`make augment-clean`**: Clean VS Code databases and modify telemetry IDs using Augment VIP
- **`make help`**: Displays all available commands with descriptions

### Configuration:
The Makefile reads configuration from `/home/<USER>/Desktop/migration/config.env` for:
- DUMP_DIR
- ENTRYPOINT_DIR
- ADMIN_DIR
- QUEUE_DIR
- ADMIN_CSB_DIR
- REPORT_BE_DIR

### Usage Example:
```bash
cd /home/<USER>/Desktop/migration
make migration
```