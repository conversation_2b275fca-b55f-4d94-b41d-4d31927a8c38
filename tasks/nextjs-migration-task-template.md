# Task: Next.js Migration/Feature Implementation

## Description
Create or migrate a feature/component/page to Next.js following the project's critical migration rules and coding standards.

## Steps
1. **Jira Ticket**: If this is a new implementation, check for or create a Jira ticket. Reference the ticket in this file.
2. **Requirements Analysis**: Review the feature for reusability. Identify if it can be used in multiple places.
3. **File Structure**: Place code in the correct directory (`components/`, `hooks/`, `utils/`, `types/`).
4. **Component Design**:
   - Use functional components only (no class components).
   - Use TypeScript strict mode.
   - Use camelCase for variables/functions, PascalCase for components.
   - Use props for flexibility and reusability.
5. **Logic Separation**:
   - Move logic to custom hooks (`hooks/`).
   - Never write logic outside `useEffect` in components.
   - Use `useMemo` for expensive calculations (not everywhere).
   - Use `useCallback` when passing functions to child components.
6. **Performance**:
   - Use Next.js `Image` component for images (avoid `<img>` tags).
   - Profile and measure performance.
   - Use async/await properly and handle errors consistently.
7. **State Management**:
   - Use Zustand/Jotai/Recoil for state (avoid Redux, Context API).
8. **Validation & Forms**:
   - Use React Hook Form + Zod for forms/validation (avoid Formik).
9. **Testing**:
   - Write tests with Vitest and Playwright.
10. **Code Quality**:
    - Use ESLint, Prettier, and Husky.
    - Add JSDoc comments.
    - Follow DRY and single responsibility principles.
11. **Edge Cases**:
    - List and handle all relevant edge cases for the feature.
12. **Documentation**:
    - Update documentation as needed.

## Edge Cases
- List all edge cases specific to this feature here.

## Jira Reference
- Ticket: [Add Jira ticket here]

---
**Why this is correct:**
- Follows all migration rules and coding standards for maintainability, performance, and scalability.
- Ensures reusability and DRY principle from the start.
- Provides traceability and process documentation for future reference.

**Why this is not correct:**
- Not applicable; this template is designed to be correct for all new tasks in this migration context.
